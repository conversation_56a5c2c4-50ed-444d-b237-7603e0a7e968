<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.iflytek.skynet</groupId>
        <artifactId>skynet-pandora-starter-parent</artifactId>
        <version>1.3.4-SNAPSHOT</version>
    </parent>

    <groupId>com.iflytek.lynxiao</groupId>
    <artifactId>lynxiao-platform</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <modules>
        <module>lynxiao-platform-data</module>
        <module>lynxiao-platform-common</module>
        <module>lynxiao-platform-resource</module>
        <module>lynxiao-platform-portal</module>
        <module>lynxiao-platform-stream</module>
        <module>lynxiao-platform-region</module>
        <module>lynxiao-platform-dispatch</module>
        <module>lynxiao-platform-featurebase</module>
        <module>lynxiao-platform-isync</module>
        <module>lynxiao-platform-test</module>
        <module>lynxiao-platform-datashard</module>
        <module>lynxiao-asset</module>
        <module>lynxiao-eval</module>
        <module>lynxiao-mcp</module>
    </modules>

    <properties>
        <revision>1.1.0</revision>
        <maven.source.skip>true</maven.source.skip>
        <maven.deploy.skip>true</maven.deploy.skip>
        <turing-gateway-feign.version>2.1.4-flames-jdk21</turing-gateway-feign.version>
        <skybox-contract.version>1.5.1</skybox-contract.version>
        <turing-astrolink.version>1.0.16.jdk21-SNAPSHOT</turing-astrolink.version>

        <mapstruct.version>1.4.2.Final</mapstruct.version>
        <commons-pool2.version>2.5.0</commons-pool2.version>
        <cloudfunc.version>1.0.0-SNAPSHOT</cloudfunc.version>
        <apache.poi.version>5.4.1</apache.poi.version>
        <langchain4j.version>0.36.2</langchain4j.version>
        <alibaba-excel.version>3.3.2</alibaba-excel.version>
        <elasticsearch-java.version>8.17.3</elasticsearch-java.version>
        <alibaba-excel.version>3.3.2</alibaba-excel.version>
        <pinyin4j.version>2.5.1</pinyin4j.version>
        <redisson.version>3.49.0</redisson.version>
    </properties>

    <repositories>
        <repository>
            <id>mvn-repo</id>
            <name>mvn-repo</name>
            <url>https://depend.iflytek.com/artifactory/mvn-repo/</url>
            <layout>default</layout>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>mvn-repo</id>
            <name>mvn-repo</name>
            <url>https://depend.iflytek.com/artifactory/mvn-repo/</url>
            <layout>default</layout>
        </pluginRepository>
    </pluginRepositories>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.iflytek.lynxiao</groupId>
                <artifactId>lynxiao-platform-data</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.iflytek.lynxiao</groupId>
                <artifactId>lynxiao-platform-vector</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.iflytek.lynxiao</groupId>
                <artifactId>lynxiao-platform-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.iflytek.lynxiao</groupId>
                <artifactId>lynxiao-platform-resource</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.iflytek.lynxiao</groupId>
                <artifactId>lynxiao-mcp-hub-sdk</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.iflytek.skybox</groupId>
                <artifactId>skybox-contract</artifactId>
                <version>${skybox-contract.version}</version>
            </dependency>

            <dependency>
                <groupId>com.iflytek.turing</groupId>
                <artifactId>turing-astrolink-starter</artifactId>
                <version>${turing-astrolink.version}</version>
            </dependency>

            <dependency>
                <groupId>com.iflytek.turing</groupId>
                <artifactId>astrolink-component-llm</artifactId>
                <version>${turing-astrolink.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>tuling-tts-grpc-sdk</artifactId>
                        <groupId>com.iflytek.tuling</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>tuling-iat-sdk</artifactId>
                        <groupId>com.iflytek.tuling</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.iflytek.turing</groupId>
                <artifactId>turing-gateway-feign</artifactId>
                <version>${turing-gateway-feign.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.persistence</groupId>
                        <artifactId>persistence-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>co.elastic.clients</groupId>
                <artifactId>elasticsearch-java</artifactId>
                <version>${elasticsearch-java.version}</version>
            </dependency>

            <dependency>
                <groupId>com.iflytek.lynxiao</groupId>
                <artifactId>cloudfunc-service</artifactId>
                <version>${cloudfunc.version}</version>
            </dependency>

            <!-- lettuce pool 缓存连接池 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${commons-pool2.version}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${apache.poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${apache.poi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${alibaba-excel.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>poi-ooxml-schemas</artifactId>
                        <groupId>org.apache.poi</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>${pinyin4j.version}</version>
            </dependency>

            <dependency>
                <groupId>com.iflytek.skynet</groupId>
                <artifactId>skynet-boot-context</artifactId>
                <version>4.3.4-SNAPSHOT</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-boot-context</artifactId>
        </dependency>
    </dependencies>

</project>
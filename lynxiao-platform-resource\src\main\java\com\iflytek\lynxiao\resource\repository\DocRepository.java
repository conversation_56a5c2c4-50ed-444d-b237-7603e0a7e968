package com.iflytek.lynxiao.resource.repository;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.data.constant.Fields;
import com.iflytek.lynxiao.data.dto.doc.DataItemBaseDTO;
import com.iflytek.lynxiao.resource.repository.dto.DocQueryDTO;
import com.iflytek.lynxiao.resource.util.AssetCellCountUtil;
import com.mongodb.BasicDBObject;
import com.mongodb.MongoBulkWriteException;
import com.mongodb.bulk.BulkWriteError;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Projections;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.BsonDocument;
import org.bson.BsonInt32;
import org.bson.BsonString;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.index.IndexDefinition;
import org.springframework.data.mongodb.core.index.IndexInfo;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.CollectionUtils;
import skynet.boot.common.service.filter.RangeFilter;
import skynet.boot.mongo.DocumentCompressor;

import java.lang.reflect.Field;
import java.net.URI;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @author: leitong
 * @date: 2024/10/24 09:19
 * @description:
 **/
@Slf4j
public class DocRepository {

    protected final MongoTemplate template;

    public DocRepository(MongoTemplate template) {
        this.template = template;
    }


    public boolean colExists(String col) {
        return this.template.collectionExists(col);
    }

    public void dropCol(String col) {
        this.template.dropCollection(col);
    }

    public <T> int bulkInsert(String collection, List<T> list) {
        log.trace("bulkInsert begin, collection={}, listSize={}", collection, list.size());
        BulkOperations bulkOperations = this.template.bulkOps(BulkOperations.BulkMode.UNORDERED, collection);
        BulkOperations insert = bulkOperations.insert(list);
        BulkWriteResult result;
        try {
            result = insert.execute();
        } catch (MongoBulkWriteException e) {
            List<BulkWriteError> errors = e.getWriteErrors(); // 每个错误项
            for (BulkWriteError error : errors) {
                int failedIndex = error.getIndex(); // 失败的是 list 中的第几个
                String message = error.getMessage(); // 错误信息
                log.error("失败位置：" + failedIndex + "，原因：" + message);
            }
            result = e.getWriteResult();
        }
        log.trace("bulkInsert finish. successCount={}", result.getInsertedCount());
        return result.getInsertedCount();
    }

    public void createCol(String col, int chunks) {
        MongoDatabase adminDB = this.template.getMongoDatabaseFactory().getMongoDatabase("admin");
        String fullColName = this.template.getDb().getName() + "." + col;
        BsonDocument shardCommand = new BsonDocument("shardCollection", new BsonString(fullColName))
                .append("key", new BsonDocument("_id", new BsonString("hashed")))  // 分片键
                .append("numInitialChunks", new BsonInt32(chunks));  // 初始分片数
        log.info("admin db execute {} : {}", shardCommand.toJson(), adminDB.runCommand(shardCommand).toJson());
        this.template.createCollection(col);
        log.info("collection {} created", col);
    }

    public Document stats(String col) {
        MongoDatabase database = this.template.getDb();
        MongoCollection<Document> collection = database.getCollection(col);
        // 执行 stats 命令
        Document stats = database.runCommand(new Document("collStats", col));
        return stats;
    }

    public void createIndex(String col, IndexDefinition index) {
        this.template.indexOps(col).ensureIndex(index);
    }

    public List<IndexInfo> listIndexes(String col) {
        return this.template.indexOps(col).getIndexInfo();
    }

    public MongoCursor<Document> findDocs(String col, Map<String, Object> queryJson) {
        return this.findDocs(col, queryJson, null, null);
    }

    public List<Document> findDocListByCond(String col, Map<String, Object> queryJson) {
        List<Document> documentList = new ArrayList<>();
        try (MongoCursor<Document> docs = this.findDocs(col, queryJson, null, null)) {
            while (docs.hasNext()) {
                documentList.add(docs.next());
            }
        }
        return documentList;
    }


    public MongoCursor<Document> findDocs(String col, Map<String, Object> queryJson, List<String> fields) {
        return this.findDocs(col, queryJson, null, fields);
    }

    public MongoCursor<Document> findDocs(String col, Map<String, Object> queryJson, Map<String, Object> sortJson, List<String> fields) {
        BasicDBObject query = new BasicDBObject(queryJson);
        BasicDBObject sort = CollectionUtils.isEmpty(sortJson) ? null : new BasicDBObject(sortJson);
        Bson projection = CollectionUtils.isEmpty(fields) ? null : Projections.include(fields);
        /**
         * noCursorTimeout只能让游标最大空闲时间为30分钟，超过三十分钟不刷新游标还是会超时。
         * https://www.mongodb.com/zh-cn/docs/manual/reference/method/cursor.noCursorTimeout/
         */
        return this.template.getCollection(col).find(query, Document.class).sort(sort).projection(projection).batchSize(500)
                .noCursorTimeout(true).cursor();
    }


    public long countDocs(String col, Map<String, Object> queryJson) {
        BasicDBObject query = new BasicDBObject(queryJson);
        return this.template.getCollection(col).countDocuments(query);
    }

    public long countDocs(String col, String cond) {
        return this.template.getCollection(col).countDocuments(BasicDBObject.parse(cond));
    }

    public long estimatedCellCount(String col, int auditStatus) {
        return AssetCellCountUtil.estimatedCount(auditStatus, this.template, col);
    }

    public long estimatedCount(String col) {
        return this.template.estimatedCount(col);
    }

    public Document findById(String col, Long id) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(id));
        return this.template.findOne(query, Document.class, col);
    }


    public <T> T insert(String col, T doc) {
        return this.template.insert(doc, col);
    }

    public <T> Collection<T> insert(String col, Collection<T> docs) {
        return this.template.insert(docs, col);
    }

    public <T> T save(String col, T doc) {
        return this.template.save(doc, col);
    }

    public long updateById(String col, Object id, Map<String, Object> doc) {
        BasicDBObject filter = new BasicDBObject();
        filter.put("_id", id);
        doc.remove("_id");
        BasicDBObject update = new BasicDBObject(doc);
        return this.template.getCollection(col).updateOne(filter, update).getModifiedCount();
    }

    public long updateByIds(String col, List<Long> ids, Map<String, Object> updateMapping) {
        BasicDBObject filter = new BasicDBObject();
        filter.put("_id", new BasicDBObject("$in", ids));
        updateMapping.remove("_id");
        BasicDBObject update = new BasicDBObject().append("$set", updateMapping);
        return this.template.getCollection(col).updateMany(filter, update).getModifiedCount();
    }

    public long deleteByQuery(String col, Map<String, Object> query) {
        return this.template.getCollection(col).deleteMany(new BasicDBObject(query)).getDeletedCount();
    }

    public boolean deleteById(String col, Long id) {
        return this.template.getCollection(col).deleteOne(new BasicDBObject().append("_id", id)).getDeletedCount() > 0;
    }

    public long deleteByIds(String col, List<Long> ids) {
        Map<String, Object> query = new BasicDBObject().append("_id", new BasicDBObject().append("$in", ids));
        return this.deleteByQuery(col, query);
    }

    public void createIndex(String collection, String field, Sort.Direction sort, boolean background) {
        Index index = new Index().on(field, sort);
        if (background) {
            index.background();
        }
        this.template.indexOps(collection)
                .ensureIndex(index);
    }

    public void deleteIndex(String collectionName, String indexName) {
        IndexOperations indexOps = this.template.indexOps(collectionName);
        indexOps.dropIndex(indexName);
    }

    public List<Document> findDocsWithLimits(String collection, DocQueryDTO dto, Integer limits) {
        log.debug("findExportDocs collection={}, criteria={}, limits={}", collection, dto, limits);
        long start = System.currentTimeMillis();
        Query query = new Query();
        query.addCriteria(buildQueryCriteria(dto));
        query.limit(limits);
        List<Document> idDocuments = this.template.find(query, Document.class, collection);
        log.info("query docs cost:{}", System.currentTimeMillis() - start);
        return idDocuments;
    }

    public List<Document> findDocsWithLimits(String collection, Criteria criteria, Integer limits) {
        long start = System.currentTimeMillis();
        Query query = new Query();
        query.addCriteria(criteria);
        query.limit(limits);
        List<Document> idDocuments = this.template.find(query, Document.class, collection);
        log.info("query docs cost:{}", System.currentTimeMillis() - start);
        return idDocuments;
    }

    public Page<Document> findDocPage(String collection, Pageable pageable, Criteria criteria) {
        // 内存排序 先查找 10页的数据，再根据_id排序，每次同页查询不一定一致（ 数据量庞大时效率低下，不再全量根据id查询排序）
        long start = System.currentTimeMillis();
        Query query = new Query();
        query.addCriteria(criteria);
        if (pageable != null) {
            query.limit(pageable.getPageSize() * 10);
            query.with(pageable.getSort());
        }
        query.fields().include("_id");
        List<Document> idDocuments = this.template.find(query, Document.class, collection);
        log.info("query ids cost:{}", System.currentTimeMillis() - start);
        long start1 = System.currentTimeMillis();
        List<Long> ids = idDocuments.stream()
                .map(doc -> (Long) doc.get("_id"))
                .collect(Collectors.toList());
        List<Long> limitIds = ids.stream().skip((long) pageable.getPageNumber() * pageable.getPageSize()).limit(pageable.getPageSize()).collect(Collectors.toList());
        Query limitQuery = new Query(Criteria.where("_id").in(limitIds));
        limitQuery.with(pageable.getSort());
        limitQuery.with(Sort.by(Sort.Order.desc("_id")));
        List<Document> documents = this.template.find(limitQuery, Document.class, collection);
        log.info("query doc cost:{}", System.currentTimeMillis() - start1);
        // post_ts 及 crawl_ts 是10位时间戳，转换为13位时间戳
        documents = documents.stream().map(document -> {
            if (document.containsKey("_id")) {
                document.put("_id", document.get("_id").toString());
            }
            Map<String, Object> decompress = DocumentCompressor.getDefault().decompress(document);
            return JSONObject.parseObject(JSONObject.toJSONString(decompress), Document.class);
            // 移除压缩字段
//            document.keySet().removeIf(key -> key.endsWith("_str_gzip"));
        }).collect(Collectors.toList());
        return new PageImpl<>(documents, pageable, this.template.getCollection(collection).estimatedDocumentCount());
    }

    public static Criteria buildQueryCriteria(DocQueryDTO dto) {
        // 初始化一个 Criteria 对象
        Criteria criteria = new Criteria();
        Class<?> clazz = dto.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            // 私有字段授权
            field.setAccessible(true);
            try {
                Object value = field.get(dto);
                if (value != null) {
                    String fieldName = field.getName();
                    // 精品等级
                    if (fieldName.equals(Fields.LEVELS)) {
                        String[] levels = ((String) value).split(",");
                        for (String level : levels) {
                            String[] split = level.split(":");
                            criteria.and("levels." + split[0]).is(Integer.valueOf(split[1]));
                        }
                        continue;
                    }
                    // 标题 || 领域
                    if (fieldName.equals(Fields.TITLE) || fieldName.equals(Fields.DOMAIN) || fieldName.equals(Fields.PATH)) {
//                        String escapedValue = Pattern.quote((String) value);
//                        criteria.and(fieldName).regex(".*" + escapedValue + ".*", "i"); // "i" 标志代表不区分大小写
                        // 使用 Pattern.quote() 来转义特殊字符
                        criteria.and(fieldName).regex(Pattern.quote(String.valueOf(value)));
                        continue;
                    }
                    // 长度 || 脚本评分
                    if (fieldName.equals(Fields.LEN) || fieldName.equals(Fields.Q_SCORE)) {
                        buildNumberCriteria(criteria, fieldName, (RangeFilter<?>) field.getType().cast(value));
                        continue;
                    }
                    // 默认处理逻辑
                    criteria.and(fieldName).is(value);
                }
            } catch (IllegalAccessException e) {
                // 异常处理逻辑
                e.printStackTrace();
            }
        }
        return criteria;
    }

    // 转换为数据预览条件
    public static DocQueryDTO toQueryDto(DataItemBaseDTO dto) throws Exception {
        DocQueryDTO queryDTO = BeanUtil.copyProperties(dto, DocQueryDTO.class);
        if (StringUtils.isNotBlank(dto.getUrl())) {
            URI url = new URI(dto.getUrl());
            queryDTO.setDomain(url.getPort() == -1 ? url.getHost() : url.getHost() + ":" + url.getPort());
            queryDTO.setProtocol(url.getScheme());
            queryDTO.setPath(dto.getUrl().substring((queryDTO.getProtocol() + "://" + queryDTO.getDomain()).length()));
        }
        return queryDTO;
    }

    // 数值过滤转换
    private static void buildNumberCriteria(Criteria criteria, String fileName, RangeFilter<?> filter) {
        if (ObjectUtils.allNull(filter.getGreaterThan(), filter.getGreaterThanOrEqual(), filter.getLessThan(), filter.getLessThanOrEqual())) {
            return;
        }
        if (filter.getGreaterThan() != null) {
            criteria.and(fileName).gt(filter.getGreaterThan());
        }
        if (filter.getGreaterThanOrEqual() != null) {
            criteria.and(fileName).gte(filter.getGreaterThanOrEqual());
        }
        if (filter.getLessThan() != null) {
            criteria.and(fileName).lt(filter.getLessThan());
        }
        if (filter.getLessThanOrEqual() != null) {
            criteria.and(fileName).lte(filter.getLessThanOrEqual());
        }
    }

}
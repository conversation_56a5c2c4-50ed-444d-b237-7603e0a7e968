--liquibase formatted sql
--changeset skynet:create_table_asset_field_item
CREATE TABLE `asset_field_item`( 
    `id` int NOT NULL AUTO_INCREMENT,
    `name` varchar(100) COMMENT '字段中文名称', 
    `field_code` varchar(100) COMMENT '属性名称，如：title', 
    `field_type` varchar(100) COMMENT '数据类型，string | int', 
    `category` varchar(100) COMMENT '类别', 
    `enabled` tinyint COMMENT '启用状态', 
    `support_vector` tinyint COMMENT '是否支持向量', 
    `support_tokenization` tinyint COMMENT '是否支持分词', 
    `support_filter` tinyint COMMENT '是否支持过滤', 
    `default_value` varchar(100) COMMENT '默认值', 
    `description` varchar(100) COMMENT '描述，如：标题', 
    `sample` varchar(100) COMMENT '样例', 
    `order_num` int COMMENT '顺序号', 
    `deleted` tinyint COMMENT '删除标志', 
    `system_field` tinyint COMMENT '是否是系统字段', 
    `created_by` varchar(100) COMMENT '创建人',
    `created_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `last_modified_by` varchar(100) COMMENT '更新人',
    `last_modified_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` varchar(100) COMMENT '租户',
    PRIMARY KEY (`id`),
    INDEX `idx_created_by` (`created_by`),
    INDEX `idx_created_date` (`created_date`),
    INDEX `idx_last_modified_by` (`last_modified_by`),
    INDEX `idx_last_modified_date` (`last_modified_date`),
    INDEX `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin;


package com.iflytek.lynxiao.resource.generated.service.impl;

import com.iflytek.lynxiao.resource.generated.domain.*; // for static metamodels
import com.iflytek.lynxiao.resource.repository.AssetFieldItemRepository;
import com.iflytek.lynxiao.resource.generated.service.GeneratedAssetFieldItemService;
import skynet.boot.exception.DuplicateNameException;
import skynet.boot.common.mapper.EntityMapper;
import skynet.boot.common.service.Criteria;
import skynet.boot.mysql.service.MysqlTemplateServiceImpl;
import com.iflytek.lynxiao.resource.service.dto.AssetFieldItemCriteria;
import com.iflytek.lynxiao.resource.service.dto.AssetFieldItemDTO;
import com.iflytek.lynxiao.resource.service.dto.AssetFieldItemPatchDTO;
import com.iflytek.lynxiao.resource.generated.service.mapper.GeneratedAssetFieldItemMapper;

import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class GeneratedAssetFieldItemServiceImpl extends MysqlTemplateServiceImpl<GeneratedAssetFieldItem, AssetFieldItemDTO, Long> implements GeneratedAssetFieldItemService {

    @Resource
    protected AssetFieldItemRepository assetFieldItemRepository;

    @Resource
    protected GeneratedAssetFieldItemMapper assetFieldItemMapper;


    public GeneratedAssetFieldItemServiceImpl(EntityMapper<AssetFieldItemDTO, GeneratedAssetFieldItem> entityMapper, JpaSpecificationExecutor<GeneratedAssetFieldItem> jpaSpecificationExecutor, JpaRepository<GeneratedAssetFieldItem, Long> jpaRepository) {
        super(entityMapper, jpaSpecificationExecutor, jpaRepository);
    }

    @Override
    public Page<AssetFieldItemDTO> findAllByCriteria(AssetFieldItemCriteria criteria, Pageable pageable) {
        return super.findAllByCriteria(criteria, null, pageable);
    }

    @Override
    public Page<AssetFieldItemDTO> findAll(Pageable pageable) {
        return super.findAllByCriteria(null, null, pageable);
    }

    @Override
    public List<AssetFieldItemDTO> findAllByCriteria(AssetFieldItemCriteria criteria, Sort sort) {
        return super.findAllByCriteria(criteria, null, sort);
    }

    @Override
    public List<AssetFieldItemDTO> findAll(Sort sort) {
        return super.findAllByCriteria(null, null, sort);
    }

    @Override
    public Specification<GeneratedAssetFieldItem> createSpecification(Criteria criteria) {
        AssetFieldItemCriteria assetFieldItemCriteria = (AssetFieldItemCriteria) criteria;
        Specification<GeneratedAssetFieldItem> specification = Specification.where(null);
        if (assetFieldItemCriteria != null) {
        }
        return specification;
    }

    @Override
    public AssetFieldItemDTO findById(Long id) {
        AssetFieldItemDTO assetFieldItemDTO = super.findById(id);
        return assetFieldItemDTO;
    }

    @Override
    public AssetFieldItemDTO save(AssetFieldItemDTO assetFieldItemDTO) {
        return super.save(assetFieldItemDTO);
    }

    @Override
    public AssetFieldItemDTO update(AssetFieldItemDTO assetFieldItemDTO) {

        return super.update(assetFieldItemDTO.getId(),assetFieldItemDTO);
    }

    @Override
    public AssetFieldItemDTO patch(AssetFieldItemPatchDTO assetFieldItemPatchDTO) {
        return super.patch(assetFieldItemPatchDTO.getId(), assetFieldItemPatchDTO);
    }

    @Override
    public void delete(Long id) {
        super.delete(id);
    }


    @Override
    public AssetFieldItemDTO copy(Long id) {
        List<String> renameFields = new ArrayList<>();
        return super.copy(id, renameFields, GeneratedAssetFieldItem.class);
    }
}
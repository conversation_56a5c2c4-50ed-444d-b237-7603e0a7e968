package com.iflytek.lynxiao.resource.generated.service.impl;

import com.iflytek.lynxiao.resource.generated.domain.*; // for static metamodels
import com.iflytek.lynxiao.resource.repository.ApplicationRepository;
import com.iflytek.lynxiao.resource.generated.service.GeneratedApplicationService;
import skynet.boot.exception.DuplicateNameException;
import skynet.boot.common.mapper.EntityMapper;
import skynet.boot.common.service.Criteria;
import skynet.boot.mysql.service.MysqlTemplateServiceImpl;
import com.iflytek.lynxiao.resource.service.dto.ApplicationCriteria;
import com.iflytek.lynxiao.resource.service.dto.ApplicationDTO;
import com.iflytek.lynxiao.resource.service.dto.ApplicationPatchDTO;
import com.iflytek.lynxiao.resource.generated.service.mapper.GeneratedApplicationMapper;

import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class GeneratedApplicationServiceImpl extends MysqlTemplateServiceImpl<GeneratedApplication, ApplicationDTO, Long> implements GeneratedApplicationService {

    @Resource
    protected ApplicationRepository applicationRepository;

    @Resource
    protected GeneratedApplicationMapper applicationMapper;


    public GeneratedApplicationServiceImpl(EntityMapper<ApplicationDTO, GeneratedApplication> entityMapper, JpaSpecificationExecutor<GeneratedApplication> jpaSpecificationExecutor, JpaRepository<GeneratedApplication, Long> jpaRepository) {
        super(entityMapper, jpaSpecificationExecutor, jpaRepository);
    }

    @Override
    public Page<ApplicationDTO> findAllByCriteria(ApplicationCriteria criteria, Pageable pageable) {
        return super.findAllByCriteria(criteria, null, pageable);
    }

    @Override
    public Page<ApplicationDTO> findAll(Pageable pageable) {
        return super.findAllByCriteria(null, null, pageable);
    }

    @Override
    public List<ApplicationDTO> findAllByCriteria(ApplicationCriteria criteria, Sort sort) {
        return super.findAllByCriteria(criteria, null, sort);
    }

    @Override
    public List<ApplicationDTO> findAll(Sort sort) {
        return super.findAllByCriteria(null, null, sort);
    }

    @Override
    public Specification<GeneratedApplication> createSpecification(Criteria criteria) {
        ApplicationCriteria applicationCriteria = (ApplicationCriteria) criteria;
        Specification<GeneratedApplication> specification = Specification.where(null);
        if (applicationCriteria != null) {
        }
        return specification;
    }

    @Override
    public ApplicationDTO findById(Long id) {
        ApplicationDTO applicationDTO = super.findById(id);
        return applicationDTO;
    }

    @Override
    public ApplicationDTO save(ApplicationDTO applicationDTO) {
        return super.save(applicationDTO);
    }

    @Override
    public ApplicationDTO update(ApplicationDTO applicationDTO) {

        return super.update(applicationDTO.getId(),applicationDTO);
    }

    @Override
    public ApplicationDTO patch(ApplicationPatchDTO applicationPatchDTO) {
        return super.patch(applicationPatchDTO.getId(), applicationPatchDTO);
    }

    @Override
    public void delete(Long id) {
        super.delete(id);
    }


    @Override
    public ApplicationDTO copy(Long id) {
        List<String> renameFields = new ArrayList<>();
        return super.copy(id, renameFields, GeneratedApplication.class);
    }
}
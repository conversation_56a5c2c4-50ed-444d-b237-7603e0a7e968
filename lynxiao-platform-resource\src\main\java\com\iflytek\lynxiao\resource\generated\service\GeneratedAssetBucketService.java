package com.iflytek.lynxiao.resource.generated.service;

import com.iflytek.lynxiao.resource.generated.domain.GeneratedAssetBucket;
import com.iflytek.lynxiao.resource.service.dto.AssetBucketCriteria;
import com.iflytek.lynxiao.resource.service.dto.AssetBucketDTO;
import com.iflytek.lynxiao.resource.service.dto.AssetBucketPatchDTO;
import skynet.boot.common.service.TemplateService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.List;

public interface GeneratedAssetBucketService extends TemplateService<GeneratedAssetBucket, AssetBucketDTO, Long> {

    /**
     * 查找列表-分页
     * @param pageable
     * @return
     */
    Page<AssetBucketDTO> findAllByCriteria(AssetBucketCriteria criteria, Pageable pageable);
    Page<AssetBucketDTO> findAll(Pageable pageable);

    /**
     * 查找列表-不分页
     * @return
     */
    List<AssetBucketDTO> findAllByCriteria(AssetBucketCriteria criteria, Sort sort);
    List<AssetBucketDTO> findAll(Sort sort);

    /**
     * 查找单条
     * @param id
     * @return
     */
    AssetBucketDTO findById(Long id);

    /**
     * 创建
     * @param assetBucketDTO
     * @return
     */
    AssetBucketDTO save(AssetBucketDTO assetBucketDTO);

    /**
     * 修改
     * @param assetBucketDTO
     */
    AssetBucketDTO update(AssetBucketDTO assetBucketDTO);

    /**
     * 更新
     * @param assetBucketPatchDTO
     */
    AssetBucketDTO patch(AssetBucketPatchDTO assetBucketPatchDTO);

    /**
     * 删除单条
     * @param id
     */
    void delete(Long id);

    /**
    * 复制
    * @param id
    */
    AssetBucketDTO copy(Long id);
}
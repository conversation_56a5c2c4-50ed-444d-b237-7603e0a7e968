package com.iflytek.lynxiao.resource.repository;

import com.iflytek.lynxiao.resource.generated.domain.GeneratedAppProduct;
import com.iflytek.lynxiao.resource.generated.repository.GeneratedAppProductRepository;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AppProductRepository extends GeneratedAppProductRepository {

    @Query("SELECT m FROM GeneratedAppProduct m WHERE m.deleted is false AND m.id = :id")
    Optional<GeneratedAppProduct> findById(@Param("id") Long id);

    @Query("SELECT m FROM GeneratedAppProduct m WHERE m.deleted is false AND m.applicationId = :applicationId")
    List<GeneratedAppProduct> findAll(@Param("applicationId") Long applicationId, Sort sort);

    @Modifying
    @Query("UPDATE GeneratedAppProduct m SET m.deleted = TRUE WHERE m.id = :id")
    void deleteById(@Param("id") Long id);

    @Modifying
    @Query("UPDATE GeneratedAppProduct m SET m.deleted = TRUE WHERE m.applicationId = :applicationId")
    void deleteByApplication(@Param("applicationId") Long applicationId);

    // 统计指定业务应用下已启用的关联产品
    @Query("SELECT COUNT(m) FROM GeneratedAppProduct m WHERE m.deleted is false AND m.enabled is true AND m.applicationId = :applicationId")
    Long countEnabled(@Param("applicationId") Long applicationId);

    // 统计指定业务应用 和 指定关联产品
    @Query("SELECT COUNT(m) FROM GeneratedAppProduct m WHERE m.deleted is false AND m.productId =:productId AND m.applicationId = :applicationId AND m.metaRegionCode = :metaRegionCode")
    Long countByProductAndApp(@Param("productId") Long productId, @Param("applicationId") Long applicationId, @Param("metaRegionCode") String metaRegionCode);

    // 统计指定产品方案
    @Query("SELECT COUNT(m) FROM GeneratedAppProduct m WHERE m.deleted is false AND m.productId =:productId")
    Long countByProduct(@Param("productId") Long productId);

    // 根据productId 查询 applicationId列表，applicationId不要重复
    @Query("SELECT DISTINCT m.applicationId FROM GeneratedAppProduct m WHERE m.deleted = false AND m.productId = :productId")
    List<Long> findApplicationIdsByProductId(@Param("productId") Long productId);

    // 根据业务应用id和启用状态查询业务关联应用记录
    List<GeneratedAppProduct> findByApplicationIdAndEnabled(Long appId, Boolean enabled);

}
ALTER TABLE `lynxiao_portal`.`asset_task_execution`
    ADD COLUMN `type` int NULL DEFAULT NULL AFTER `status`;

ALTER TABLE `lynxiao_portal`.`asset_task_execution`
    ADD COLUMN `execution_id` bigint NULL DEFAULT NULL AFTER `status`;

ALTER TABLE `lynxiao_portal`.`asset_task_execution`
    ADD COLUMN `step` int NULL DEFAULT NULL AFTER `status`;

ALTER TABLE `lynxiao_portal`.`asset_task_execution`
    ADD COLUMN `message` text NULL DEFAULT NULL AFTER `status`;

ALTER TABLE `lynxiao_portal`.`asset_task_execution`
    ADD COLUMN `auto` tinyint NULL DEFAULT NULL AFTER `status`;

UPDATE lynxiao_portal.asset_task_execution as a left JOIN lynxiao_portal.asset_task as b on a.task_id = b.id  set a.type = b.type

UPDATE lynxiao_portal.asset_task_execution SET execution_id = id;

ALTER TABLE `lynxiao_portal`.asset_task_execution ADD INDEX idx_execution_step (execution_id, step);
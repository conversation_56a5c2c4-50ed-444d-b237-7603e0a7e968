package com.iflytek.lynxiao.resource.generated.service;

import com.iflytek.lynxiao.resource.generated.domain.GeneratedApplication;
import com.iflytek.lynxiao.resource.service.dto.ApplicationCriteria;
import com.iflytek.lynxiao.resource.service.dto.ApplicationDTO;
import com.iflytek.lynxiao.resource.service.dto.ApplicationPatchDTO;
import skynet.boot.common.service.TemplateService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.List;

public interface GeneratedApplicationService extends TemplateService<GeneratedApplication, ApplicationDTO, Long> {

    /**
     * 查找列表-分页
     * @param pageable
     * @return
     */
    Page<ApplicationDTO> findAllByCriteria(ApplicationCriteria criteria, Pageable pageable);
    Page<ApplicationDTO> findAll(Pageable pageable);

    /**
     * 查找列表-不分页
     * @return
     */
    List<ApplicationDTO> findAllByCriteria(ApplicationCriteria criteria, Sort sort);
    List<ApplicationDTO> findAll(Sort sort);

    /**
     * 查找单条
     * @param id
     * @return
     */
    ApplicationDTO findById(Long id);

    /**
     * 创建
     * @param applicationDTO
     * @return
     */
    ApplicationDTO save(ApplicationDTO applicationDTO);

    /**
     * 修改
     * @param applicationDTO
     */
    ApplicationDTO update(ApplicationDTO applicationDTO);

    /**
     * 更新
     * @param applicationPatchDTO
     */
    ApplicationDTO patch(ApplicationPatchDTO applicationPatchDTO);

    /**
     * 删除单条
     * @param id
     */
    void delete(Long id);

    /**
    * 复制
    * @param id
    */
    ApplicationDTO copy(Long id);
}
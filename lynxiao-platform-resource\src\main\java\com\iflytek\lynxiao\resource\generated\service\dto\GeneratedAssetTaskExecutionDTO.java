package com.iflytek.lynxiao.resource.generated.service.dto;

import lombok.*;
import skynet.boot.mongo.domain.AbstractAuditingEntity;
import java.time.Instant;
import io.swagger.v3.oas.annotations.media.Schema;

@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class GeneratedAssetTaskExecutionDTO extends AbstractAuditingEntity<Long> {

    /**
     * 关联任务ID
     */
    @Schema(title = "关联任务ID")
    private Long taskId;

    /**
     * 流程快照
     */
    @Schema(title = "流程快照")
    private Long flowId;

    /**
     * 星链流程ID
     */
    @Schema(title = "星链流程ID")
    private String astrolinkId;

    /**
     * 源桶列表
     */
    @Schema(title = "源桶列表")
    private String sourceBuckets;

    /**
     * 任务状态
     */
    @Schema(title = "任务状态")
    private Integer status;

    /**
     * 错误信息
     */
    @Schema(title = "错误信息")
    private String message;

    /**
     * 子任务步骤
     */
    @Schema(title = "子任务步骤")
    private Integer step;

    /**
     * 执行记录ID
     */
    @Schema(title = "执行记录ID")
    private Long executionId;

    /**
     * 任务类型
     */
    @Schema(title = "任务类型")
    private Integer type;

    /**
     * 是否为调试
     */
    @Schema(title = "是否为调试")
    private Integer debug;

    /**
     * 开始时间
     */
    @Schema(title = "开始时间")
    private Instant startTime;

    /**
     * 结束时间
     */
    @Schema(title = "结束时间")
    private Instant endTime;

    /**
     * 任务总量
     */
    @Schema(title = "任务总量")
    private Long total;

    /**
     * 已处理数据量
     */
    @Schema(title = "已处理数据量")
    private Long done;

    /**
     * 失败数据量
     */
    @Schema(title = "失败数据量")
    private Long fail;

    /**
     * 应该收到几次任务总量的消息，也就是流程中源桶的个数
     */
    @Schema(title = "应该收到几次任务总量的消息，也就是流程中源桶的个数")
    private Integer totalShould;

    /**
     * 实际收到几次的任务总量的消息
     */
    @Schema(title = "实际收到几次的任务总量的消息")
    private Integer totalReal;

    /**
     * 是否自动
     */
    @Schema(title = "是否自动")
    private Boolean auto;

    /**
     * 执行参数，仅非流程任务会保存
     */
    @Schema(title = "执行参数，仅非流程任务会保存")
    private String executeParam;
}
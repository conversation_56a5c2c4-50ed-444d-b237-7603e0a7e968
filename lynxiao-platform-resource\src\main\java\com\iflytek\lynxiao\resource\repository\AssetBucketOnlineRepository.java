package com.iflytek.lynxiao.resource.repository;

import com.iflytek.lynxiao.resource.generated.domain.GeneratedAssetBucketOnline;
import com.iflytek.lynxiao.resource.generated.repository.GeneratedAssetBucketOnlineRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
@Transactional
public interface AssetBucketOnlineRepository extends GeneratedAssetBucketOnlineRepository {

    @Query("SELECT m FROM GeneratedAssetBucketOnline m WHERE m.deleted is false AND m.id = :id AND m.status != 3")
    Optional<GeneratedAssetBucketOnline> findById(@Param("id") Long id);

    @Query("SELECT m FROM GeneratedAssetBucketOnline m WHERE m.deleted is false AND m.bucketCode = :bucketCode AND m.status != 3")
    List<GeneratedAssetBucketOnline> findByBucketCode(@Param("bucketCode") String bucketCode);

    @Query("SELECT m FROM GeneratedAssetBucketOnline m WHERE m.deleted is false AND m.bucketCode = :bucketCode AND m.targetRegion = :targetRegion AND m.status != 3")
    Optional<GeneratedAssetBucketOnline> findByBucketCodeAndTargetRegion(@Param("bucketCode") String bucketCode, @Param("targetRegion") String targetRegion);

    @Query("SELECT COUNT(m) FROM GeneratedAssetBucketOnline m WHERE m.deleted is false AND m.name =:name AND m.status != 3")
    Long countByName(@Param("name") String name);

    @Query("SELECT COUNT(m) FROM GeneratedAssetBucketOnline  m WHERE m.deleted is false AND m.targetRegion =:targetRegion AND m.bucketCode =:bucketCode AND m.status != 3")
    Long countByTargetRegionAndBucketCode(@Param("targetRegion") String targetRegion, @Param("bucketCode") String bucketCode);

    @Query("SELECT m FROM GeneratedAssetBucketOnline m " +
            "WHERE m.deleted = false " +
            "AND (:enabled IS NULL OR m.enabled = :enabled) " +
            "AND (:status IS NULL OR m.status = :status) " +
            "AND (:targetRegion IS NULL OR m.targetRegion = :targetRegion) " +
            "AND (:search IS NULL OR LOWER(m.name) LIKE LOWER(CONCAT('%', :search, '%')))")
    Page<GeneratedAssetBucketOnline> search(@Param("search") String search, @Param("enabled") Boolean enabled, @Param("targetRegion") String targetRegion, @Param("status") Integer status, Pageable pageable);

    Page<GeneratedAssetBucketOnline> findAllByDeletedFalse(Pageable pageable);

    List<GeneratedAssetBucketOnline> findAllByDeletedFalse();

    @Modifying
    @Query("UPDATE GeneratedAssetBucketOnline m SET m.deleted = TRUE WHERE m.id = :id")
    void deleteById(@Param("id") Long id);

    @Query("SELECT m FROM GeneratedAssetBucketOnline m WHERE m.deleted is false AND m.code = :code AND m.status != 3")
    Optional<GeneratedAssetBucketOnline> findByCodeAndDeletedFalse(@Param("code") String code);

}
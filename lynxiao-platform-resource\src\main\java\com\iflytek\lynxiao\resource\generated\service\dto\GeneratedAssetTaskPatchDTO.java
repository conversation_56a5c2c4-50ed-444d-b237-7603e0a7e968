package com.iflytek.lynxiao.resource.generated.service.dto;

import lombok.Getter;
import lombok.Setter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import skynet.boot.common.dto.AbstractPatchDTO;


@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class GeneratedAssetTaskPatchDTO extends AbstractPatchDTO<Long> {
    /**
     * 关联流程ID
     */
    @Schema(title = "关联流程ID")
    private Long flowId;

    /**
     * 星链流程ID
     */
    @Schema(title = "星链流程ID")
    private String astrolinkId;

    /**
     * 定时任务表达式
     */
    @Schema(title = "定时任务表达式")
    private String cron;

    /**
     * 触发器
     */
    @Schema(title = "触发器")
    private String trigger;

    public GeneratedAssetTaskPatchDTO() {
        super();
    }
}
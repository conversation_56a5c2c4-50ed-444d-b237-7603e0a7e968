package com.iflytek.lynxiao.resource.repository;

import com.iflytek.lynxiao.resource.generated.domain.GeneratedAssetTaskExecution;
import com.iflytek.lynxiao.resource.generated.repository.GeneratedAssetTaskExecutionRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface AssetTaskExecutionRepository extends GeneratedAssetTaskExecutionRepository {

    /**
     * 查询任务执行记录
     *
     * @param taskId 任务ID
     * @return 任务执行记录列表
     */
    List<GeneratedAssetTaskExecution> findAllByTaskId(Long taskId);

    /**
     * 查询任务执行记录
     *
     * @param taskId   任务ID
     * @param pageable 分页信息
     * @return 任务执行记录列表
     */
    Page<GeneratedAssetTaskExecution> findAllByTaskId(Long taskId, Pageable pageable);

    /**
     * 查询任务执行记录
     *
     * @param taskIds  任务ID列表
     * @param pageable 分页信息
     * @return 任务执行记录列表
     */
    Page<GeneratedAssetTaskExecution> findAllByTaskIdIn(List<Long> taskIds, Pageable pageable);

    /**
     * 查询任务执行记录
     *
     * @param taskIds  任务ID列表
     * @param pageable 分页信息
     * @return 任务执行记录列表
     */
    @Query("select e FROM GeneratedAssetTaskExecution e WHERE e.taskId IN :taskIds AND (:debug IS NULL OR e.debug=:debug) AND (:statusList IS NULL OR e.status IN :statusList)")
    Page<GeneratedAssetTaskExecution> findAllByTaskIds(@Param("taskIds") List<Long> taskIds,
                                                       @Param("debug") Integer debug,
                                                       @Param("statusList") List<Integer> statusList,
                                                       Pageable pageable);

    /**
     * 查询所有任务执行记录
     *
     * @param debug    是否为调试模式
     * @param pageable 分页信息
     * @return 任务执行记录列表
     */
    @Query("select e FROM GeneratedAssetTaskExecution e WHERE (:debug is null or e.debug=:debug) AND (:statusList is null OR e.status IN :statusList)")
    Page<GeneratedAssetTaskExecution> findAllByDebugAndStatusIn(@Param("debug") Integer debug,
                                                                @Param("statusList") List<Integer> statusList,
                                                                Pageable pageable);

    /**
     * 查询任务执行记录
     *
     * @param taskId   任务ID
     * @param pageable 分页信息
     * @return 任务执行记录列表
     */
    Page<GeneratedAssetTaskExecution> findAllByTaskIdAndDebug(Long taskId, Integer debug, Pageable pageable);

    /**
     * 查询任务执行记录
     *
     * @param pageable 分页信息
     * @return 任务执行记录列表
     */
    Page<GeneratedAssetTaskExecution> findAllByTaskIdInAndDebug(List<Long> taskIds, Integer debug, Pageable pageable);

    /**
     * 查询任务执行记录
     *
     * @param pageable 分页信息
     * @return 任务执行记录列表
     */
    Page<GeneratedAssetTaskExecution> findAllByStatusIn(List<Integer> statuses, Pageable pageable);

    /**
     * 查询任务执行记录
     *
     * @param taskId   任务ID
     * @param statuses 状态列表
     * @return 任务执行记录列表
     */
    List<GeneratedAssetTaskExecution> findAllByTaskIdAndStatusIn(Long taskId, List<Integer> statuses);

    /**
     * 查询任务执行记录
     *
     * @param taskId   任务ID
     * @param statuses 状态列表
     * @return 任务执行记录列表
     */
    List<GeneratedAssetTaskExecution> findAllByTaskIdAndDebugAndStatusIn(Long taskId, Integer debug,
                                                                         List<Integer> statuses);

    /**
     * 查询任务执行记录
     *
     * @param taskId   任务ID
     * @param statuses 状态列表
     * @return 满足状态的最新任务执行记录
     */
    Optional<GeneratedAssetTaskExecution> findFirstByTaskIdAndStatusInOrderByStartTimeDesc(Long taskId,
                                                                                           List<Integer> statuses);

    /**
     * 查询任务执行记录
     *
     * @param taskId   任务ID
     * @param statuses 状态列表
     * @return 满足状态的最新任务执行记录
     */
    List<GeneratedAssetTaskExecution> findByTaskIdAndStatusInOrderByStartTimeDesc(Long taskId, List<Integer> statuses);


    int deleteByTaskIdIn(List<Long> taskIds);

    /**
     * 查询任务最近的执行记录，优先返回指定状态记录
     *
     * @param taskId 任务ID
     * @return 任务最近的执行记录
     */
    @Query(value = "SELECT * FROM asset_task_execution WHERE execution_id = (SELECT execution_id FROM asset_task_execution WHERE task_id = :taskId And debug = 0 ORDER BY CASE WHEN status IN (:status) THEN 0 ELSE 1 END, start_time DESC LIMIT 1)", nativeQuery = true)
    List<GeneratedAssetTaskExecution> findLastExecutions(@Param("taskId") Long taskId,@Param("status") List<Integer> status);


    /**
     * 查询任务最近的N条执行记录
     *
     * @param taskIds 任务ID列表
     * @param debug   是否调试模式
     * @param limit   限制条数
     * @return 任务最近的N条执行记录
     */
    @Query(value = "SELECT a.* FROM asset_task_execution a WHERE a.task_id IN :taskIds AND a.debug = :debug ORDER BY" + " a.start_time DESC LIMIT :limit", nativeQuery = true)
    List<GeneratedAssetTaskExecution> findLastNExecutions(List<Long> taskIds, Integer debug, Integer limit);

    @Query("select e FROM GeneratedAssetTaskExecution e WHERE e.executionId = :executionId AND (:step IS NULL OR e.step=:step)")
    Optional<GeneratedAssetTaskExecution> findAllByExecutionIdAndStep(Long executionId, Integer step);

    List<GeneratedAssetTaskExecution> findAllByExecutionId(Long executionId);

    @Modifying
    @Transactional
    @Query("UPDATE GeneratedAssetTaskExecution e SET e.status = :status, e.lastModifiedDate = :now WHERE e.id = :id")
    int updateStatus(@Param("id") Long id, @Param("status") int status, @Param("now") Instant now);

    /**
     * 更新 total 字段
     */
    @Modifying
    @Transactional
    @Query("UPDATE GeneratedAssetTaskExecution e SET e.total = :total, e.totalReal = e.totalReal + 1, e.lastModifiedDate = :now WHERE e.id = :id")
    int updateTotal(@Param("id") Long id, @Param("total") Long total, @Param("now") Instant now);

    /**
     * 更新 done 字段
     */
    @Modifying
    @Transactional
    @Query("UPDATE GeneratedAssetTaskExecution e SET e.done = :done, e.lastModifiedDate = :now WHERE e.id = :id")
    int updateDone(@Param("id") Long id, @Param("done") Long done, @Param("now") Instant now);

    /**
     * 更新 fail 字段
     */
    @Modifying
    @Transactional
    @Query("UPDATE GeneratedAssetTaskExecution e SET e.fail = :fail, e.lastModifiedDate = :now WHERE e.id = :id")
    int updateFail(@Param("id") Long id, @Param("fail") Long fail, @Param("now") Instant now);

    /**
     * 更新为完成
     */
    @Modifying
    @Transactional
    @Query("UPDATE GeneratedAssetTaskExecution e SET e.status = 2 , e.lastModifiedDate = :now, e.endTime = :now WHERE e.id = :id AND e.status <> 2")
    int updateComplete(@Param("id") Long id, @Param("now") Instant now);

    /**
     * 更新为异常
     */
    @Modifying
    @Transactional
    @Query("UPDATE GeneratedAssetTaskExecution e SET e.status = -3 , e.lastModifiedDate = now() WHERE e.id = :id")
    int updateException(@Param("id") Long id);

}
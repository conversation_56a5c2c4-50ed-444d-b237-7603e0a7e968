package com.iflytek.lynxiao.resource.generated.service.impl;

import com.iflytek.lynxiao.resource.generated.domain.*; // for static metamodels
import com.iflytek.lynxiao.resource.repository.AppProductRepository;
import com.iflytek.lynxiao.resource.generated.service.GeneratedAppProductService;
import skynet.boot.exception.DuplicateNameException;
import skynet.boot.common.mapper.EntityMapper;
import skynet.boot.common.service.Criteria;
import skynet.boot.mysql.service.MysqlTemplateServiceImpl;
import com.iflytek.lynxiao.resource.service.dto.AppProductCriteria;
import com.iflytek.lynxiao.resource.service.dto.AppProductDTO;
import com.iflytek.lynxiao.resource.service.dto.AppProductPatchDTO;
import com.iflytek.lynxiao.resource.generated.service.mapper.GeneratedAppProductMapper;

import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class GeneratedAppProductServiceImpl extends MysqlTemplateServiceImpl<GeneratedAppProduct, AppProductDTO, Long> implements GeneratedAppProductService {

    @Resource
    protected AppProductRepository appProductRepository;

    @Resource
    protected GeneratedAppProductMapper appProductMapper;


    public GeneratedAppProductServiceImpl(EntityMapper<AppProductDTO, GeneratedAppProduct> entityMapper, JpaSpecificationExecutor<GeneratedAppProduct> jpaSpecificationExecutor, JpaRepository<GeneratedAppProduct, Long> jpaRepository) {
        super(entityMapper, jpaSpecificationExecutor, jpaRepository);
    }

    @Override
    public Page<AppProductDTO> findAllByCriteria(AppProductCriteria criteria, Pageable pageable) {
        return super.findAllByCriteria(criteria, null, pageable);
    }

    @Override
    public Page<AppProductDTO> findAll(Pageable pageable) {
        return super.findAllByCriteria(null, null, pageable);
    }

    @Override
    public List<AppProductDTO> findAllByCriteria(AppProductCriteria criteria, Sort sort) {
        return super.findAllByCriteria(criteria, null, sort);
    }

    @Override
    public List<AppProductDTO> findAll(Sort sort) {
        return super.findAllByCriteria(null, null, sort);
    }

    @Override
    public Specification<GeneratedAppProduct> createSpecification(Criteria criteria) {
        AppProductCriteria appProductCriteria = (AppProductCriteria) criteria;
        Specification<GeneratedAppProduct> specification = Specification.where(null);
        if (appProductCriteria != null) {
        }
        return specification;
    }

    @Override
    public AppProductDTO findById(Long id) {
        AppProductDTO appProductDTO = super.findById(id);
        return appProductDTO;
    }

    @Override
    public AppProductDTO save(AppProductDTO appProductDTO) {
        return super.save(appProductDTO);
    }

    @Override
    public AppProductDTO update(AppProductDTO appProductDTO) {

        return super.update(appProductDTO.getId(),appProductDTO);
    }

    @Override
    public AppProductDTO patch(AppProductPatchDTO appProductPatchDTO) {
        return super.patch(appProductPatchDTO.getId(), appProductPatchDTO);
    }

    @Override
    public void delete(Long id) {
        super.delete(id);
    }


    @Override
    public AppProductDTO copy(Long id) {
        List<String> renameFields = new ArrayList<>();
        return super.copy(id, renameFields, GeneratedAppProduct.class);
    }
}
--liquibase formatted sql
--changeset skynet:create_table_app_product
CREATE TABLE `app_product`( 
    `id` int NOT NULL AUTO_INCREMENT,
    `app_id` varchar(100) COMMENT '应用id', 
    `product_id` bigint COMMENT '产品方案id', 
    `application_id` bigint COMMENT '业务应用id application.id', 
    `meta_region_code` varchar(100) COMMENT '区域编码', 
    `concurrent_quota` int COMMENT '并发配额', 
    `qps_limit` bigint COMMENT 'QPS使用限制', 
    `qps_unit` int COMMENT 'QPS限制单位，1：天  2：小时，3：分钟', 
    `request_limit` bigint COMMENT '调用量限制，-1：无限量', 
    `auth_deadline` datetime COMMENT '授权有效期', 
    `enabled` tinyint COMMENT '是否启用', 
    `protocol` varchar(100) COMMENT '协议', 
    `deleted` tinyint COMMENT '删除标志', 
    `description` text COMMENT '描述', 
    `created_by` varchar(100) COMMENT '创建人',
    `created_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `last_modified_by` varchar(100) COMMENT '更新人',
    `last_modified_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` varchar(100) COMMENT '租户',
    PRIMARY KEY (`id`),
    INDEX `idx_created_by` (`created_by`),
    INDEX `idx_created_date` (`created_date`),
    INDEX `idx_last_modified_by` (`last_modified_by`),
    INDEX `idx_last_modified_date` (`last_modified_date`),
    INDEX `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin;


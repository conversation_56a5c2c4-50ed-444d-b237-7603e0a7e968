package com.iflytek.lynxiao.resource.repository;

import com.iflytek.lynxiao.resource.generated.domain.GeneratedOnlineIdxDbInst;
import com.iflytek.lynxiao.resource.generated.repository.GeneratedOnlineIdxDbInstRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface OnlineIdxDbInstRepository extends GeneratedOnlineIdxDbInstRepository {

    @Query("SELECT m FROM GeneratedOnlineIdxDbInst m WHERE m.deleted is false AND m.id = :id")
    Optional<GeneratedOnlineIdxDbInst> findById(@Param("id") Long id);

    @Query("SELECT m FROM GeneratedOnlineIdxDbInst m " +
            "WHERE m.deleted = false " +
            "AND (:enabled IS NULL OR m.enabled = :enabled) " +
            "AND (:status IS NULL OR m.status = :status) " +
            "AND (:targetRegion IS NULL OR m.targetRegion = :targetRegion) " +
            "AND (:search IS NULL OR :search = '' OR LOWER(m.name) LIKE LOWER(CONCAT('%', :search, '%')))")
    Page<GeneratedOnlineIdxDbInst> search(@Param("search") String search, @Param("enabled") Boolean enabled, @Param("targetRegion") String targetRegion, @Param("status") Integer status, Pageable pageable);

    Page<GeneratedOnlineIdxDbInst> findAllByDeletedFalse(Pageable pageable);

    @Modifying
    @Query("UPDATE GeneratedOnlineIdxDbInst m SET m.deleted = TRUE WHERE m.id = :id")
    void deleteById(@Param("id") Long id);

    @Query("SELECT COUNT(m) FROM GeneratedOnlineIdxDbInst  m WHERE m.deleted is false AND m.idxDbInstId =:idxDbInstId AND m.status != 3")
    Long countByIdx(@Param("idxDbInstId") Long idxDbInstId);

    @Query("SELECT COUNT(m) FROM GeneratedOnlineIdxDbInst  m WHERE m.deleted is false AND m.name =:name AND m.status != 3")
    Long countByName(@Param("name") String name);

    @Query("SELECT COUNT(m) FROM GeneratedOnlineIdxDbInst  m WHERE m.deleted is false AND m.targetRegion =:targetRegion AND m.idxDbInstId =:idxDbInstId AND m.status != 3")
    Long countByTargetRegionAndIdxDbInstId(@Param("targetRegion") String targetRegion, @Param("idxDbInstId") Long idxDbInstId);

    @Query("SELECT m FROM GeneratedOnlineIdxDbInst m WHERE m.deleted is false AND (m.taskId in :taskIds OR m.replayTaskId in :taskIds)")
    List<GeneratedOnlineIdxDbInst> findByGenTaskIdOrDedupTaskId(@Param("taskIds") List<Long> taskIds);

    @Query("SELECT m FROM GeneratedOnlineIdxDbInst m WHERE m.deleted IS FALSE AND m.clusterId IS NOT NULL " +
            "AND m.idInRegion IS NOT NULL AND m.idxDbInstId = :idxDbInstId")
    List<GeneratedOnlineIdxDbInst> findByIdxDbInstId(@Param("idxDbInstId") Long idxDbInstId);

    @Query("SELECT m FROM GeneratedOnlineIdxDbInst m WHERE m.deleted IS FALSE AND m.clusterId IS NOT NULL " +
            "AND m.idInRegion IS NOT NULL AND m.taskId IS NOT NULL AND m.idxDbInstId = :idxDbInstId")
    List<GeneratedOnlineIdxDbInst> findSyncTask(@Param("idxDbInstId") Long idxDbInstId);

    @Query("SELECT COUNT(m) FROM GeneratedOnlineIdxDbInst m WHERE m.deleted is false AND m.status != 3 AND (m.sourceRegion =:code OR m.targetRegion =:code)")
    Long countByRegion(@Param("code") String regionCode);

    @Query("SELECT m FROM GeneratedOnlineIdxDbInst m WHERE m.deleted IS FALSE AND m.clusterId IS NOT NULL AND " +
            "m.idInRegion IS NOT NULL AND m.replayTaskId IS NOT NULL")
    List<GeneratedOnlineIdxDbInst> findReplayTask();
}
package com.iflytek.lynxiao.resource.generated.service.impl;

import com.iflytek.lynxiao.resource.generated.domain.*; // for static metamodels
import com.iflytek.lynxiao.resource.repository.AssetBucketRepository;
import com.iflytek.lynxiao.resource.generated.service.GeneratedAssetBucketService;
import skynet.boot.exception.DuplicateNameException;
import skynet.boot.common.mapper.EntityMapper;
import skynet.boot.common.service.Criteria;
import skynet.boot.mysql.service.MysqlTemplateServiceImpl;
import com.iflytek.lynxiao.resource.service.dto.AssetBucketCriteria;
import com.iflytek.lynxiao.resource.service.dto.AssetBucketDTO;
import com.iflytek.lynxiao.resource.service.dto.AssetBucketPatchDTO;
import com.iflytek.lynxiao.resource.generated.service.mapper.GeneratedAssetBucketMapper;

import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class GeneratedAssetBucketServiceImpl extends MysqlTemplateServiceImpl<GeneratedAssetBucket, AssetBucketDTO, Long> implements GeneratedAssetBucketService {

    @Resource
    protected AssetBucketRepository assetBucketRepository;

    @Resource
    protected GeneratedAssetBucketMapper assetBucketMapper;


    public GeneratedAssetBucketServiceImpl(EntityMapper<AssetBucketDTO, GeneratedAssetBucket> entityMapper, JpaSpecificationExecutor<GeneratedAssetBucket> jpaSpecificationExecutor, JpaRepository<GeneratedAssetBucket, Long> jpaRepository) {
        super(entityMapper, jpaSpecificationExecutor, jpaRepository);
    }

    @Override
    public Page<AssetBucketDTO> findAllByCriteria(AssetBucketCriteria criteria, Pageable pageable) {
        return super.findAllByCriteria(criteria, null, pageable);
    }

    @Override
    public Page<AssetBucketDTO> findAll(Pageable pageable) {
        return super.findAllByCriteria(null, null, pageable);
    }

    @Override
    public List<AssetBucketDTO> findAllByCriteria(AssetBucketCriteria criteria, Sort sort) {
        return super.findAllByCriteria(criteria, null, sort);
    }

    @Override
    public List<AssetBucketDTO> findAll(Sort sort) {
        return super.findAllByCriteria(null, null, sort);
    }

    @Override
    public Specification<GeneratedAssetBucket> createSpecification(Criteria criteria) {
        AssetBucketCriteria assetBucketCriteria = (AssetBucketCriteria) criteria;
        Specification<GeneratedAssetBucket> specification = Specification.where(null);
        if (assetBucketCriteria != null) {
        }
        return specification;
    }

    @Override
    public AssetBucketDTO findById(Long id) {
        AssetBucketDTO assetBucketDTO = super.findById(id);
        return assetBucketDTO;
    }

    @Override
    public AssetBucketDTO save(AssetBucketDTO assetBucketDTO) {
        return super.save(assetBucketDTO);
    }

    @Override
    public AssetBucketDTO update(AssetBucketDTO assetBucketDTO) {

        return super.update(assetBucketDTO.getId(),assetBucketDTO);
    }

    @Override
    public AssetBucketDTO patch(AssetBucketPatchDTO assetBucketPatchDTO) {
        return super.patch(assetBucketPatchDTO.getId(), assetBucketPatchDTO);
    }

    @Override
    public void delete(Long id) {
        super.delete(id);
    }


    @Override
    public AssetBucketDTO copy(Long id) {
        List<String> renameFields = new ArrayList<>();
        return super.copy(id, renameFields, GeneratedAssetBucket.class);
    }
}
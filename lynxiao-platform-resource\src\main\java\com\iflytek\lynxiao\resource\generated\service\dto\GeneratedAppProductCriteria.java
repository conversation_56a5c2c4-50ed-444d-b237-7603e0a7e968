package com.iflytek.lynxiao.resource.generated.service.dto;

import java.io.Serializable;

import skynet.boot.common.service.Criteria;
import skynet.boot.common.service.filter.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GeneratedAppProductCriteria implements Serializable, Criteria  {

    public GeneratedAppProductCriteria() {}

    public GeneratedAppProductCriteria(GeneratedAppProductCriteria other) {
    }

    @Override
    public GeneratedAppProductCriteria copy() {
        return new GeneratedAppProductCriteria(this);
    }
}

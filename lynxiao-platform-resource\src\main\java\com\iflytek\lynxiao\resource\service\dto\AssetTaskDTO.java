package com.iflytek.lynxiao.resource.service.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.EqualsAndHashCode;
import com.iflytek.lynxiao.resource.generated.service.dto.GeneratedAssetTaskDTO;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class AssetTaskDTO extends GeneratedAssetTaskDTO {

    /**
     * 最近一次执行记录
     */
    private List<AssetTaskExecutionDTO> lastExecution;
}

package com.iflytek.lynxiao.resource.repository;

import com.iflytek.lynxiao.resource.generated.domain.GeneratedAssetFieldItem;
import com.iflytek.lynxiao.resource.generated.repository.GeneratedAssetFieldItemRepository;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AssetFieldItemRepository extends GeneratedAssetFieldItemRepository {


    Optional<GeneratedAssetFieldItem> findByIdAndDeletedFalse(Long id);

    // 查询未删除的数据，并根据searchText模糊匹配name和fieldName字段，同时可以根据category、vector、tokenization、filter字段筛选
    @Query("SELECT m FROM GeneratedAssetFieldItem m WHERE m.deleted is false " +
            "AND (LOWER(m.fieldCode) LIKE LOWER(CONCAT('%', :searchText, '%')) OR LOWER(m.name) LIKE LOWER(CONCAT('%', :searchText, '%')))" +
            "AND (:vector IS NULL OR m.supportVector = :vector) " +
            "AND (:tokenization IS NULL OR m.supportTokenization = :tokenization) " +
            "AND (:filter IS NULL OR m.supportFilter = :filter) " +
            "AND (:systemField IS NULL OR m.systemField = :systemField) ")
    List<GeneratedAssetFieldItem> findAllBySearchText(@Param("searchText") String searchText,
                                                      @Param("vector") Boolean vector,
                                                      @Param("tokenization") Boolean tokenization,
                                                      @Param("filter") Boolean filter,
                                                      @Param("systemField") Boolean systemField,
                                                      Sort sort);

    // 查询未删除的数据，并根据vector、tokenization、filter、systemField、supportIndex字段筛选
    @Query("SELECT m FROM GeneratedAssetFieldItem m WHERE m.deleted is false " +
           "AND (:vector IS NULL OR m.supportVector = :vector) " +
           "AND (:tokenization IS NULL OR m.supportTokenization = :tokenization) " +
           "AND (:filter IS NULL OR m.supportFilter = :filter) " +
           "AND (:systemField IS NULL OR m.systemField = :systemField) ")
    List<GeneratedAssetFieldItem> findAllByDeletedFalse(@Param("vector") Boolean vector,
                                                        @Param("tokenization") Boolean tokenization,
                                                        @Param("filter") Boolean filter,
                                                        @Param("systemField") Boolean systemField,
                                                        Sort sort);

    // 获取所有有值的类别，注意category是一个列表序列化成一个字符串，帮我处理一下
    @Query("SELECT DISTINCT m.category FROM GeneratedAssetFieldItem m WHERE m.category IS NOT NULL")
    List<String> findDistinctCategories();

    // 字段是否存在
    long countByFieldCodeAndDeletedFalse(String fieldCode);


    // 名称是否存在
    long countByNameAndDeletedFalse(String name);

    @Query("SELECT MAX(m.orderNum) FROM GeneratedAssetFieldItem m WHERE m.deleted is false")
    Integer findMaxOrderNum();

    @Modifying
    @Query("UPDATE GeneratedAssetFieldItem m SET m.deleted = TRUE WHERE m.id = :id")
    void deleteById(@Param("id") Long id);
}
package com.iflytek.lynxiao.resource.generated.service.dto;

import java.io.Serializable;

import skynet.boot.common.service.Criteria;
import skynet.boot.common.service.filter.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GeneratedAssetBucketCriteria implements Serializable, Criteria  {

    public GeneratedAssetBucketCriteria() {}

    public GeneratedAssetBucketCriteria(GeneratedAssetBucketCriteria other) {
    }

    @Override
    public GeneratedAssetBucketCriteria copy() {
        return new GeneratedAssetBucketCriteria(this);
    }
}

package com.iflytek.lynxiao.resource.generated.service.dto;

import lombok.*;
import skynet.boot.mongo.domain.AbstractAuditingEntity;
import io.swagger.v3.oas.annotations.media.Schema;

@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class GeneratedAssetFieldItemDTO extends AbstractAuditingEntity<Long> {

    /**
     * 字段中文名称
     */
    @Schema(title = "字段中文名称")
    private String name;

    /**
     * 属性名称，如：title
     */
    @Schema(title = "属性名称，如：title")
    private String fieldCode;

    /**
     * 数据类型，string | int
     */
    @Schema(title = "数据类型，string | int")
    private String fieldType;

    /**
     * 类别
     */
    @Schema(title = "类别")
    private String category;

    /**
     * 启用状态
     */
    @Schema(title = "启用状态")
    private Boolean enabled;

    /**
     * 是否支持向量
     */
    @Schema(title = "是否支持向量")
    private Boolean supportVector;

    /**
     * 是否支持分词
     */
    @Schema(title = "是否支持分词")
    private Boolean supportTokenization;

    /**
     * 是否支持过滤
     */
    @Schema(title = "是否支持过滤")
    private Boolean supportFilter;

    /**
     * 默认值
     */
    @Schema(title = "默认值")
    private String defaultValue;

    /**
     * 描述，如：标题
     */
    @Schema(title = "描述，如：标题")
    private String description;

    /**
     * 样例
     */
    @Schema(title = "样例")
    private String sample;

    /**
     * 顺序号
     */
    @Schema(title = "顺序号")
    private Integer orderNum;

    /**
     * 删除标志
     */
    @Schema(title = "删除标志")
    private Boolean deleted;

    /**
     * 是否是系统字段
     */
    @Schema(title = "是否是系统字段")
    private Boolean systemField;
}
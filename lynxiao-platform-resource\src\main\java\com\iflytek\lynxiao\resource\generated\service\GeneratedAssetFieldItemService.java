package com.iflytek.lynxiao.resource.generated.service;

import com.iflytek.lynxiao.resource.generated.domain.GeneratedAssetFieldItem;
import com.iflytek.lynxiao.resource.service.dto.AssetFieldItemCriteria;
import com.iflytek.lynxiao.resource.service.dto.AssetFieldItemDTO;
import com.iflytek.lynxiao.resource.service.dto.AssetFieldItemPatchDTO;
import skynet.boot.common.service.TemplateService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.List;

public interface GeneratedAssetFieldItemService extends TemplateService<GeneratedAssetFieldItem, AssetFieldItemDTO, Long> {

    /**
     * 查找列表-分页
     * @param pageable
     * @return
     */
    Page<AssetFieldItemDTO> findAllByCriteria(AssetFieldItemCriteria criteria, Pageable pageable);
    Page<AssetFieldItemDTO> findAll(Pageable pageable);

    /**
     * 查找列表-不分页
     * @return
     */
    List<AssetFieldItemDTO> findAllByCriteria(AssetFieldItemCriteria criteria, Sort sort);
    List<AssetFieldItemDTO> findAll(Sort sort);

    /**
     * 查找单条
     * @param id
     * @return
     */
    AssetFieldItemDTO findById(Long id);

    /**
     * 创建
     * @param assetFieldItemDTO
     * @return
     */
    AssetFieldItemDTO save(AssetFieldItemDTO assetFieldItemDTO);

    /**
     * 修改
     * @param assetFieldItemDTO
     */
    AssetFieldItemDTO update(AssetFieldItemDTO assetFieldItemDTO);

    /**
     * 更新
     * @param assetFieldItemPatchDTO
     */
    AssetFieldItemDTO patch(AssetFieldItemPatchDTO assetFieldItemPatchDTO);

    /**
     * 删除单条
     * @param id
     */
    void delete(Long id);

    /**
    * 复制
    * @param id
    */
    AssetFieldItemDTO copy(Long id);
}
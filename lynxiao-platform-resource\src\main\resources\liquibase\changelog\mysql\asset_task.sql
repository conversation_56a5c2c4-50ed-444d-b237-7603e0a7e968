--liquibase formatted sql
--changeset skynet:create_table_asset_task
CREATE TABLE `asset_task`( 
    `id` int NOT NULL AUTO_INCREMENT,
    `bucket_code` varchar(100) COMMENT '关联桶编码', 
    `flow_id` bigint COMMENT '关联流程ID', 
    `astrolink_id` varchar(100) COMMENT '星链流程ID', 
    `cron` varchar(100) COMMENT '定时任务表达式', 
    `type` int COMMENT '任务类型', 
    `trigger` varchar(100) COMMENT '触发器', 
    `created_by` varchar(100) COMMENT '创建人',
    `created_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `last_modified_by` varchar(100) COMMENT '更新人',
    `last_modified_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` varchar(100) COMMENT '租户',
    PRIMARY KEY (`id`),
    INDEX `idx_created_by` (`created_by`),
    INDEX `idx_created_date` (`created_date`),
    INDEX `idx_last_modified_by` (`last_modified_by`),
    INDEX `idx_last_modified_date` (`last_modified_date`),
    INDEX `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin;


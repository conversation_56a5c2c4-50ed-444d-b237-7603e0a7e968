package com.iflytek.lynxiao.resource.generated.service;

import com.iflytek.lynxiao.resource.generated.domain.GeneratedAppProduct;
import com.iflytek.lynxiao.resource.service.dto.AppProductCriteria;
import com.iflytek.lynxiao.resource.service.dto.AppProductDTO;
import com.iflytek.lynxiao.resource.service.dto.AppProductPatchDTO;
import skynet.boot.common.service.TemplateService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.List;

public interface GeneratedAppProductService extends TemplateService<GeneratedAppProduct, AppProductDTO, Long> {

    /**
     * 查找列表-分页
     * @param pageable
     * @return
     */
    Page<AppProductDTO> findAllByCriteria(AppProductCriteria criteria, Pageable pageable);
    Page<AppProductDTO> findAll(Pageable pageable);

    /**
     * 查找列表-不分页
     * @return
     */
    List<AppProductDTO> findAllByCriteria(AppProductCriteria criteria, Sort sort);
    List<AppProductDTO> findAll(Sort sort);

    /**
     * 查找单条
     * @param id
     * @return
     */
    AppProductDTO findById(Long id);

    /**
     * 创建
     * @param appProductDTO
     * @return
     */
    AppProductDTO save(AppProductDTO appProductDTO);

    /**
     * 修改
     * @param appProductDTO
     */
    AppProductDTO update(AppProductDTO appProductDTO);

    /**
     * 更新
     * @param appProductPatchDTO
     */
    AppProductDTO patch(AppProductPatchDTO appProductPatchDTO);

    /**
     * 删除单条
     * @param id
     */
    void delete(Long id);

    /**
    * 复制
    * @param id
    */
    AppProductDTO copy(Long id);
}
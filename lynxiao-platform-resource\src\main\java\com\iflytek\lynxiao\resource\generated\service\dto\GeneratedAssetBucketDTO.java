package com.iflytek.lynxiao.resource.generated.service.dto;

import lombok.*;
import skynet.boot.mongo.domain.AbstractAuditingEntity;
import io.swagger.v3.oas.annotations.media.Schema;

@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class GeneratedAssetBucketDTO extends AbstractAuditingEntity<Long> {

    /**
     * 桶名称
     */
    @Schema(title = "桶名称")
    private String name;

    /**
     * 桶编码，命名空间编码+资产类型编码+12位随机编码
     */
    @Schema(title = "桶编码，命名空间编码+资产类型编码+12位随机编码")
    private String code;

    /**
     * 桶描述
     */
    @Schema(title = "桶描述")
    private String description;

    /**
     * 桶英文名称
     */
    @Schema(title = "桶英文名称")
    private String enName;

    /**
     * 资产类型, 如 SITE，DOC
     */
    @Schema(title = "资产类型, 如 SITE，DOC")
    private String kindCode;

    /**
     * 存储周期，单位小时, 如：最近7天，0 为无限期；
     */
    @Schema(title = "存储周期，单位小时, 如：最近7天，0 为无限期；")
    private Long ttl;

    /**
     * 审核方式: 1：先审 、2：后审
     */
    @Schema(title = "审核方式: 1：先审 、2：后审")
    private Integer auditType;

    /**
     * 是否支持修订（桶内数据是否能编辑修改更新）
     */
    @Schema(title = "是否支持修订（桶内数据是否能编辑修改更新）")
    private Boolean supportCorrect;

    /**
     * 标签
     */
    @Schema(title = "标签")
    private String tags;

    /**
     * 数据列字段（数据规格）
     */
    @Schema(title = "数据列字段（数据规格）")
    private String cellFields;

    /**
     * 索引属性
     */
    @Schema(title = "索引属性")
    private String indexFields;

    /**
     * 是否启用
     */
    @Schema(title = "是否启用")
    private Boolean enabled;

    /**
     * 状态 1: 草稿；2：已发布
     */
    @Schema(title = "状态 1: 草稿；2：已发布")
    private Integer status;

    /**
     * 扩展属性，不同的数据类型属性不一样，由各自解释
     */
    @Schema(title = "扩展属性，不同的数据类型属性不一样，由各自解释")
    private String extConfig;

    /**
     * 归属空间编码
     */
    @Schema(title = "归属空间编码")
    private String spaceCode;

    /**
     * 归属模块编码
     */
    @Schema(title = "归属模块编码")
    private String moduleCode;

    /**
     * 归属目录编码
     */
    @Schema(title = "归属目录编码")
    private String catalogCode;

    /**
     * 顺序号
     */
    @Schema(title = "顺序号")
    private Integer orderNum;

    /**
     * 删除标志
     */
    @Schema(title = "删除标志")
    private Boolean deleted;

    /**
     * 规格模板
     */
    @Schema(title = "规格模板")
    private String fieldSpec;
}
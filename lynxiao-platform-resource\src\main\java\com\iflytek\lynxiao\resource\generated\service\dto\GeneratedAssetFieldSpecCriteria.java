package com.iflytek.lynxiao.resource.generated.service.dto;

import java.io.Serializable;

import skynet.boot.common.service.Criteria;
import skynet.boot.common.service.filter.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GeneratedAssetFieldSpecCriteria implements Serializable, Criteria  {

    public GeneratedAssetFieldSpecCriteria() {}

    public GeneratedAssetFieldSpecCriteria(GeneratedAssetFieldSpecCriteria other) {
    }

    @Override
    public GeneratedAssetFieldSpecCriteria copy() {
        return new GeneratedAssetFieldSpecCriteria(this);
    }
}

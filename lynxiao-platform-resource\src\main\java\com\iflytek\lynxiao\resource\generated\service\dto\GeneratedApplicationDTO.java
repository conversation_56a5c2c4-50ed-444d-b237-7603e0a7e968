package com.iflytek.lynxiao.resource.generated.service.dto;

import lombok.*;
import skynet.boot.mongo.domain.AbstractAuditingEntity;
import io.swagger.v3.oas.annotations.media.Schema;

@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class GeneratedApplicationDTO extends AbstractAuditingEntity<Long> {

    /**
     * 应用id
     */
    @Schema(title = "应用id")
    private String appId;

    /**
     * 应用secret
     */
    @Schema(title = "应用secret")
    private String appSecret;

    /**
     * 网关应用id
     */
    @Schema(title = "网关应用id")
    private String gwId;

    /**
     * 名称
     */
    @Schema(title = "名称")
    private String name;

    /**
     * 描述
     */
    @Schema(title = "描述")
    private String description;

    /**
     * 邮件列表
     */
    @Schema(title = "邮件列表")
    private String emails;

    /**
     * 类型
     */
    @Schema(title = "类型")
    private String type;

    /**
     * 是否启用
     */
    @Schema(title = "是否启用")
    private Boolean enabled;

    /**
     * 删除标志
     */
    @Schema(title = "删除标志")
    private Boolean deleted;

    /**
     * mcp开关
     */
    @Schema(title = "mcp开关")
    private Boolean mcp;
}
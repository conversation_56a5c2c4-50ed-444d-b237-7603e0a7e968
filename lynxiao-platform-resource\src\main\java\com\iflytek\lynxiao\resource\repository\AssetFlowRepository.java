package com.iflytek.lynxiao.resource.repository;

import com.iflytek.lynxiao.resource.generated.domain.GeneratedAssetFlow;
import com.iflytek.lynxiao.resource.generated.repository.GeneratedAssetFlowRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AssetFlowRepository extends GeneratedAssetFlowRepository {

    // 找到指定id，且未删除的版本
    Optional<GeneratedAssetFlow> findByIdAndDeletedFalse(Long id);

    @Query("SELECT m FROM GeneratedAssetFlow m WHERE m.bucketCode = :bucketCode AND m.type = :type AND (LOWER(m.name) LIKE LOWER(CONCAT('%', :search, '%')) OR LOWER(m.code) LIKE LOWER(CONCAT('%', :search, '%')))")
    Page<GeneratedAssetFlow> search(@Param("bucketCode") String bucketCode, @Param("search") String search, @Param("type") Integer type, Pageable pageable);

    @Query("SELECT m FROM GeneratedAssetFlow m WHERE m.bucketCode = :bucketCode AND m.type = :type AND m.deleted = false")
    Page<GeneratedAssetFlow> findAllByPage(@Param("bucketCode") String bucketCode, @Param("type") Integer type, Pageable pageable);

    @Query("SELECT m FROM GeneratedAssetFlow m WHERE m.bucketCode = :bucketCode AND m.deleted = false")
    List<GeneratedAssetFlow> findAllByBucketCode(@Param("bucketCode") String bucketCode);

    @Query("SELECT m FROM GeneratedAssetFlow m WHERE m.bucketCode = :bucketCode AND m.type = :type AND m.enabled = true AND m.deleted = false")
    List<GeneratedAssetFlow> findEnabledByType(@Param("bucketCode") String bucketCode, @Param("type") Integer type, Sort sort);


    // 查询最大的version
    @Query("SELECT MAX(m.version) FROM GeneratedAssetFlow m WHERE m.bucketCode = :bucketCode AND m.deleted = false")
    Optional<Integer> findMaxVersionByBucketCode(@Param("bucketCode") String bucketCode);

    @Modifying
    @Query("UPDATE GeneratedAssetFlow m SET m.enabled = false WHERE m.bucketCode = :bucketCode AND m.deleted = false AND m.type = 0")
    void updateDisabled(@Param("bucketCode") String bucketCode);

    @Modifying
    @Query("UPDATE GeneratedAssetFlow m SET m.deleted = TRUE WHERE m.id = :id")
    void deleteById(@Param("id") Long id);

    @Modifying
    @Query("UPDATE GeneratedAssetFlow m SET m.deleted = TRUE WHERE m.bucketCode = :bucketCode")
    void deleteByBucketCode(@Param("bucketCode") String bucketCode);

}
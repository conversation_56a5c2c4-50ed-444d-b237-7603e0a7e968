package com.iflytek.lynxiao.resource.generated.service.dto;

import lombok.*;
import skynet.boot.mongo.domain.AbstractAuditingEntity;
import io.swagger.v3.oas.annotations.media.Schema;

@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class GeneratedAssetFieldSpecDTO extends AbstractAuditingEntity<Long> {

    /**
     * 名称
     */
    @Schema(title = "名称")
    private String name;

    /**
     * 编码
     */
    @Schema(title = "编码")
    private String code;

    /**
     * 描述
     */
    @Schema(title = "描述")
    private String description;

    /**
     * 启用状态
     */
    @Schema(title = "启用状态")
    private Boolean enabled;

    /**
     * 属性字段列表，站点属性表 meta_site_fields记录
     */
    @Schema(title = "属性字段列表，站点属性表 meta_site_fields记录")
    private String fields;

    /**
     * 删除标志
     */
    @Schema(title = "删除标志")
    private Boolean deleted;

    /**
     * 支持的桶类型
     */
    @Schema(title = "支持的桶类型")
    private String type;
}
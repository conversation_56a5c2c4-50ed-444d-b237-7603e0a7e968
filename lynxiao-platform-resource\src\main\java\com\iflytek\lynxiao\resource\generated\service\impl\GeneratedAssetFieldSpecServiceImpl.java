package com.iflytek.lynxiao.resource.generated.service.impl;

import com.iflytek.lynxiao.resource.generated.domain.*; // for static metamodels
import com.iflytek.lynxiao.resource.repository.AssetFieldSpecRepository;
import com.iflytek.lynxiao.resource.generated.service.GeneratedAssetFieldSpecService;
import skynet.boot.exception.DuplicateNameException;
import skynet.boot.common.mapper.EntityMapper;
import skynet.boot.common.service.Criteria;
import skynet.boot.mysql.service.MysqlTemplateServiceImpl;
import com.iflytek.lynxiao.resource.service.dto.AssetFieldSpecCriteria;
import com.iflytek.lynxiao.resource.service.dto.AssetFieldSpecDTO;
import com.iflytek.lynxiao.resource.service.dto.AssetFieldSpecPatchDTO;
import com.iflytek.lynxiao.resource.generated.service.mapper.GeneratedAssetFieldSpecMapper;

import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class GeneratedAssetFieldSpecServiceImpl extends MysqlTemplateServiceImpl<GeneratedAssetFieldSpec, AssetFieldSpecDTO, Long> implements GeneratedAssetFieldSpecService {

    @Resource
    protected AssetFieldSpecRepository assetFieldSpecRepository;

    @Resource
    protected GeneratedAssetFieldSpecMapper assetFieldSpecMapper;


    public GeneratedAssetFieldSpecServiceImpl(EntityMapper<AssetFieldSpecDTO, GeneratedAssetFieldSpec> entityMapper, JpaSpecificationExecutor<GeneratedAssetFieldSpec> jpaSpecificationExecutor, JpaRepository<GeneratedAssetFieldSpec, Long> jpaRepository) {
        super(entityMapper, jpaSpecificationExecutor, jpaRepository);
    }

    @Override
    public Page<AssetFieldSpecDTO> findAllByCriteria(AssetFieldSpecCriteria criteria, Pageable pageable) {
        return super.findAllByCriteria(criteria, null, pageable);
    }

    @Override
    public Page<AssetFieldSpecDTO> findAll(Pageable pageable) {
        return super.findAllByCriteria(null, null, pageable);
    }

    @Override
    public List<AssetFieldSpecDTO> findAllByCriteria(AssetFieldSpecCriteria criteria, Sort sort) {
        return super.findAllByCriteria(criteria, null, sort);
    }

    @Override
    public List<AssetFieldSpecDTO> findAll(Sort sort) {
        return super.findAllByCriteria(null, null, sort);
    }

    @Override
    public Specification<GeneratedAssetFieldSpec> createSpecification(Criteria criteria) {
        AssetFieldSpecCriteria assetFieldSpecCriteria = (AssetFieldSpecCriteria) criteria;
        Specification<GeneratedAssetFieldSpec> specification = Specification.where(null);
        if (assetFieldSpecCriteria != null) {
        }
        return specification;
    }

    @Override
    public AssetFieldSpecDTO findById(Long id) {
        AssetFieldSpecDTO assetFieldSpecDTO = super.findById(id);
        return assetFieldSpecDTO;
    }

    @Override
    public AssetFieldSpecDTO save(AssetFieldSpecDTO assetFieldSpecDTO) {
        return super.save(assetFieldSpecDTO);
    }

    @Override
    public AssetFieldSpecDTO update(AssetFieldSpecDTO assetFieldSpecDTO) {

        return super.update(assetFieldSpecDTO.getId(),assetFieldSpecDTO);
    }

    @Override
    public AssetFieldSpecDTO patch(AssetFieldSpecPatchDTO assetFieldSpecPatchDTO) {
        return super.patch(assetFieldSpecPatchDTO.getId(), assetFieldSpecPatchDTO);
    }

    @Override
    public void delete(Long id) {
        super.delete(id);
    }


    @Override
    public AssetFieldSpecDTO copy(Long id) {
        List<String> renameFields = new ArrayList<>();
        return super.copy(id, renameFields, GeneratedAssetFieldSpec.class);
    }
}
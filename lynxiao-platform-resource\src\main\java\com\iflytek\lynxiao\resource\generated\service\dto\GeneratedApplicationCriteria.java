package com.iflytek.lynxiao.resource.generated.service.dto;

import java.io.Serializable;

import skynet.boot.common.service.Criteria;
import skynet.boot.common.service.filter.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GeneratedApplicationCriteria implements Serializable, Criteria  {

    public GeneratedApplicationCriteria() {}

    public GeneratedApplicationCriteria(GeneratedApplicationCriteria other) {
    }

    @Override
    public GeneratedApplicationCriteria copy() {
        return new GeneratedApplicationCriteria(this);
    }
}

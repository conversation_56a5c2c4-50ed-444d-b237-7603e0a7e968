package com.iflytek.lynxiao.resource.generated.service;

import com.iflytek.lynxiao.resource.generated.domain.GeneratedAssetFieldSpec;
import com.iflytek.lynxiao.resource.service.dto.AssetFieldSpecCriteria;
import com.iflytek.lynxiao.resource.service.dto.AssetFieldSpecDTO;
import com.iflytek.lynxiao.resource.service.dto.AssetFieldSpecPatchDTO;
import skynet.boot.common.service.TemplateService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.List;

public interface GeneratedAssetFieldSpecService extends TemplateService<GeneratedAssetFieldSpec, AssetFieldSpecDTO, Long> {

    /**
     * 查找列表-分页
     * @param pageable
     * @return
     */
    Page<AssetFieldSpecDTO> findAllByCriteria(AssetFieldSpecCriteria criteria, Pageable pageable);
    Page<AssetFieldSpecDTO> findAll(Pageable pageable);

    /**
     * 查找列表-不分页
     * @return
     */
    List<AssetFieldSpecDTO> findAllByCriteria(AssetFieldSpecCriteria criteria, Sort sort);
    List<AssetFieldSpecDTO> findAll(Sort sort);

    /**
     * 查找单条
     * @param id
     * @return
     */
    AssetFieldSpecDTO findById(Long id);

    /**
     * 创建
     * @param assetFieldSpecDTO
     * @return
     */
    AssetFieldSpecDTO save(AssetFieldSpecDTO assetFieldSpecDTO);

    /**
     * 修改
     * @param assetFieldSpecDTO
     */
    AssetFieldSpecDTO update(AssetFieldSpecDTO assetFieldSpecDTO);

    /**
     * 更新
     * @param assetFieldSpecPatchDTO
     */
    AssetFieldSpecDTO patch(AssetFieldSpecPatchDTO assetFieldSpecPatchDTO);

    /**
     * 删除单条
     * @param id
     */
    void delete(Long id);

    /**
    * 复制
    * @param id
    */
    AssetFieldSpecDTO copy(Long id);
}
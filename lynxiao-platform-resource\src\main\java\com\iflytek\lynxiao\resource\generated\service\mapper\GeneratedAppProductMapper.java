package com.iflytek.lynxiao.resource.generated.service.mapper;

import com.iflytek.lynxiao.resource.generated.domain.GeneratedAppProduct;
import com.iflytek.lynxiao.resource.service.dto.AppProductDTO;
import skynet.boot.common.mapper.EntityMapper;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;

@Mapper(
    componentModel = "spring",
    uses = {},
    builder = @Builder(disableBuilder = true))
public interface GeneratedAppProductMapper extends EntityMapper<AppProductDTO, GeneratedAppProduct> {

}
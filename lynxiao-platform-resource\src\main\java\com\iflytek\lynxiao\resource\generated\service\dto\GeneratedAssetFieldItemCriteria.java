package com.iflytek.lynxiao.resource.generated.service.dto;

import java.io.Serializable;

import skynet.boot.common.service.Criteria;
import skynet.boot.common.service.filter.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GeneratedAssetFieldItemCriteria implements Serializable, Criteria  {

    public GeneratedAssetFieldItemCriteria() {}

    public GeneratedAssetFieldItemCriteria(GeneratedAssetFieldItemCriteria other) {
    }

    @Override
    public GeneratedAssetFieldItemCriteria copy() {
        return new GeneratedAssetFieldItemCriteria(this);
    }
}

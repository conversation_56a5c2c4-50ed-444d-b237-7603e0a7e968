package com.iflytek.lynxiao.stream.action.dedup.core;

import com.iflytek.lynxiao.stream.action.dedup.config.DedupProperties;
import com.iflytek.lynxiao.data.constant.ScriptType;
import org.springframework.context.ApplicationContext;

/**
 * <AUTHOR>  2024/10/26 15:21
 */
public class DedupScriptManager {

    private final DedupProperties properties;

    private final ApplicationContext applicationContext;

    public DedupScriptManager(DedupProperties properties, ApplicationContext applicationContext) {
        this.properties = properties;
        this.applicationContext = applicationContext;
    }


    public DedupScriptExecutor getExecutor() {
        if (properties.getDocConfig().getScriptType() == ScriptType.MOCK) {
            return applicationContext.getBean(DedupScriptExecutor4Mock.class);
        }
        return applicationContext.getBean(DedupScriptExecutor4Groovy.class);
    }
}

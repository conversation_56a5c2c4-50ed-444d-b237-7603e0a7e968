package com.iflytek.lynxiao.resource.generated.service.dto;

import lombok.*;
import skynet.boot.mongo.domain.AbstractAuditingEntity;
import io.swagger.v3.oas.annotations.media.Schema;

@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class GeneratedAssetTaskDTO extends AbstractAuditingEntity<Long> {

    /**
     * 关联桶编码
     */
    @Schema(title = "关联桶编码")
    private String bucketCode;

    /**
     * 关联流程ID
     */
    @Schema(title = "关联流程ID")
    private Long flowId;

    /**
     * 星链流程ID
     */
    @Schema(title = "星链流程ID")
    private String astrolinkId;

    /**
     * 定时任务表达式
     */
    @Schema(title = "定时任务表达式")
    private String cron;

    /**
     * 任务类型
     */
    @Schema(title = "任务类型")
    private Integer type;

    /**
     * 触发器
     */
    @Schema(title = "触发器")
    private String trigger;
}
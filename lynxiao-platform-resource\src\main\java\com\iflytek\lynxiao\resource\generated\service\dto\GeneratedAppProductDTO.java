package com.iflytek.lynxiao.resource.generated.service.dto;

import lombok.*;
import skynet.boot.mongo.domain.AbstractAuditingEntity;
import java.time.Instant;
import io.swagger.v3.oas.annotations.media.Schema;

@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class GeneratedAppProductDTO extends AbstractAuditingEntity<Long> {

    /**
     * 应用id
     */
    @Schema(title = "应用id")
    private String appId;

    /**
     * 产品方案id
     */
    @Schema(title = "产品方案id")
    private Long productId;

    /**
     * 业务应用id application.id
     */
    @Schema(title = "业务应用id application.id")
    private Long applicationId;

    /**
     * 区域编码
     */
    @Schema(title = "区域编码")
    private String metaRegionCode;

    /**
     * 并发配额
     */
    @Schema(title = "并发配额")
    private Integer concurrentQuota;

    /**
     * QPS使用限制
     */
    @Schema(title = "QPS使用限制")
    private Long qpsLimit;

    /**
     * QPS限制单位，1：天  2：小时，3：分钟
     */
    @Schema(title = "QPS限制单位，1：天  2：小时，3：分钟")
    private Integer qpsUnit;

    /**
     * 调用量限制，-1：无限量
     */
    @Schema(title = "调用量限制，-1：无限量")
    private Long requestLimit;

    /**
     * 授权有效期
     */
    @Schema(title = "授权有效期")
    private Instant authDeadline;

    /**
     * 是否启用
     */
    @Schema(title = "是否启用")
    private Boolean enabled;

    /**
     * 协议
     */
    @Schema(title = "协议")
    private String protocol;

    /**
     * 删除标志
     */
    @Schema(title = "删除标志")
    private Boolean deleted;

    /**
     * 描述
     */
    @Schema(title = "描述")
    private String description;
}
package com.iflytek.lynxiao.stream.utils;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.data.constant.Fields;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;
import skynet.boot.common.utils.IdUtil;

import java.io.BufferedReader;
import java.io.FileReader;

/**
 * 插入mongo数据
 *
 * @author: cwruan
 * @Date: 2025-08-01 18:00
 */
public class InsertMongoUtil {
    public static String mongoUrl = "**************************************************************************************************************************************************************";
    public static String collectionName = "ODS5VXJUC_004";
    public static String jsonTemplate = """
            {
              "s": 0,
              "_f": 0,
              "_op": "insert",
              "_e": 0,
              "levels": {
                "L06": 4
              },
              "url":""
            }
            """;

    public static void main(String[] args) {
        try {
            MongoTemplate template = new MongoTemplate(new SimpleMongoClientDatabaseFactory(mongoUrl));
            try (BufferedReader reader = new BufferedReader(new FileReader("/Users/<USER>/Downloads/基孔肯亚热.json"))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    template.insert(parseLine(line), collectionName);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
//        System.out.println(System.currentTimeMillis());
//        System.out.println(IdUtil.getSnowflakeNextId());

//        System.out.println(parseLine("{\"title\": \"基孔肯雅热的疾病名称是什么？\", \"content\": \"基孔肯雅热\"}"));

    }

    private static JSONObject parseLine(String line) {
        JSONObject lineJson = JSONObject.parseObject(line);
        JSONObject docJson = JSONObject.parseObject(jsonTemplate);
        docJson.put("_id", IdUtil.getSnowflakeNextId());
        docJson.put("content", lineJson.getString("content"));
        docJson.put("title", lineJson.getString("title"));
        docJson.put("_ts", System.currentTimeMillis());
        docJson.put("gid", GidUtil.generateGid(docJson.getString("content")));
        JSONObject ctx = JSONObject.of(Fields.SPAN, new long[]{0, docJson.getString(Fields.CONTENT).length()});
        JSONObject s = JSONObject.of("content", docJson.getString(Fields.CONTENT), "ctx", ctx);
        docJson.put(Fields.SPLIT_CONTENT, JSONArray.of(s));
        return docJson;
    }
}
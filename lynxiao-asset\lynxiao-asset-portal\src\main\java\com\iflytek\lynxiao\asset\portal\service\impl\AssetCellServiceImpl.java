package com.iflytek.lynxiao.asset.portal.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.asset.portal.domain.AssetKind;
import com.iflytek.lynxiao.asset.portal.dto.cell.CellDeleteDTO;
import com.iflytek.lynxiao.asset.portal.dto.cell.CellDetailQueryDTO;
import com.iflytek.lynxiao.asset.portal.dto.cell.CellEntryDTO;
import com.iflytek.lynxiao.asset.portal.dto.cell.CellExportDTO;
import com.iflytek.lynxiao.asset.portal.service.AssetCellService;
import com.iflytek.lynxiao.asset.portal.service.cell.AssetCellQueryFactory;
import com.iflytek.lynxiao.asset.portal.service.cell.AssetDocCellOperator;
import com.iflytek.lynxiao.asset.portal.service.storage.AssetMongo;
import com.iflytek.lynxiao.asset.portal.utils.CriteriaUtil;
import com.iflytek.lynxiao.common.datashard.DatashardDecoder;
import com.iflytek.lynxiao.common.datashard.DatashardEncoder;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.common.snowflake.SnowflakeIdGenerator;
import com.iflytek.lynxiao.common.utils.ExprUtil;
import com.iflytek.lynxiao.data.domain.AssetAuditStatus;
import com.iflytek.lynxiao.data.domain.AssetAuditor;
import com.iflytek.lynxiao.data.domain.AssetCell;
import com.iflytek.lynxiao.data.domain.AssetCellProps;
import com.iflytek.lynxiao.data.dto.asset.AssetKindCode;
import com.iflytek.lynxiao.data.dto.asset.CellQueryDTO;
import com.iflytek.lynxiao.data.dto.asset.QueryInvalidCellCountDTO;
import com.iflytek.lynxiao.data.dto.featurebase.DocDetailDTO;
import com.iflytek.lynxiao.resource.generated.domain.GeneratedAssetBucket;
import com.iflytek.lynxiao.resource.repository.AssetBucketRepository;
import com.iflytek.lynxiao.resource.repository.AssetKindRepository;
import com.iflytek.lynxiao.resource.repository.DocDatasetRepository;
import com.iflytek.lynxiao.resource.repository.DocRepository;
import com.mongodb.client.MongoCursor;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import skynet.boot.mongo.DocumentCompressor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>  2025/5/28 17:09
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class AssetCellServiceImpl implements AssetCellService {

    private final AssetBucketRepository bucketRepository;
    private final DatashardDecoder datashardDecoder;
    private final DatashardEncoder datashardEncoder;
    private final AssetCellQueryFactory cellQueryFactory;
    private final AssetMongo assetMongo;
    private final AssetKindRepository kindRepository;
    private final AssetDocCellOperator docCellOperator;
    private final SnowflakeIdGenerator snowflakeIdGenerator;
    private final DocRepository docRepository;


    public AssetCellServiceImpl(AssetBucketRepository bucketRepository,
                                DatashardDecoder datashardDecoder,
                                DatashardEncoder datashardEncoder,
                                AssetCellQueryFactory cellQueryFactory,
                                AssetMongo assetMongo, AssetKindRepository kindRepository,
                                AssetDocCellOperator docCellOperator,
                                SnowflakeIdGenerator snowflakeIdGenerator, DocRepository docRepository) {
        this.bucketRepository = bucketRepository;
        this.datashardDecoder = datashardDecoder;
        this.datashardEncoder = datashardEncoder;
        this.cellQueryFactory = cellQueryFactory;
        this.assetMongo = assetMongo;
        this.kindRepository = kindRepository;
        this.docCellOperator = docCellOperator;
        this.snowflakeIdGenerator = snowflakeIdGenerator;
        this.docRepository = docRepository;
    }


    @Override
    public Long count(CellQueryDTO dto) {
        log.debug("Counting cells with query: {}", dto);
        return cellQueryFactory.buildCellQuery(findBucket(dto.getBucketCode()))
                .count(findBucket(dto.getBucketCode()), dto);
    }

    @Override
    public Long invalidCount(QueryInvalidCellCountDTO dto) {
        log.debug("Invalidating cells with query .dto: {}", dto);
        return cellQueryFactory.buildCellQuery(findBucket(dto.getBucketCode()))
                .invalidCount(findBucket(dto.getBucketCode()), dto);
    }

    @Override
    public Page<AssetCell> list(CellQueryDTO dto, Pageable pageable) {
        log.debug("Listing cells with query: {}, pageable: {}", dto, pageable);
        GeneratedAssetBucket bucket = findBucket(dto.getBucketCode());
        Page<AssetCell> cellList = cellQueryFactory.buildCellQuery(bucket).findByPage(bucket, dto, pageable);
        return decodePageDoc(cellList);
    }

    @Override
    public DocDetailDTO detail(CellDetailQueryDTO dto) {
        GeneratedAssetBucket bucket = findBucket(dto.getBucketCode());
        return cellQueryFactory.buildCellQuery(bucket).detail(bucket, dto);
    }

    @Override
    public void delete(CellDeleteDTO dto) {
        log.info("Deleting cells in bucket: {}, dto: {}", dto.getFilter().getBucketCode(), dto);
        GeneratedAssetBucket bucket = findBucket(dto.getFilter().getBucketCode());
        if (AssetKindCode.IDX.equals(bucket.getKindCode())) {
            throw new LynxiaoException("索引库数据单元不允许删除!");
        }
        AssetKind assetKind = findAssetKind(bucket.getKindCode());
        MongoTemplate mongoTemplate = this.assetMongo.getMongoTemplate(assetKind.getStorageConfig());
        Query query = new Query();
        query.fields().include(AssetCell.ID);
        if (CollectionUtils.isEmpty(dto.getFilter().getIds())) {
            // 删除所有符合条件的数据单元
            query.addCriteria(CriteriaUtil.build(dto.getFilter()));
        } else {
            // 删除列表里的数据单元
            query.addCriteria(Criteria.where(AssetCell.ID)
                    .in(dto.getFilter().getIds().stream().map(Long::valueOf).toList()));
        }

        if (AssetKindCode.DOC.equals(bucket.getKindCode())) {
            query.fields().include(AssetCell.GRID_FS_ID);
            List<AssetCell> cells = mongoTemplate.find(query, AssetCell.class, bucket.getCode());
            List<String> gfsIds = cells.stream().map(AssetCell::getGridFsId).toList();
            this.docCellOperator.deleteFiles(bucket.getCode(), gfsIds);
            if (!CollectionUtils.isEmpty(dto.getFilter().getFileIds())) {
                // 同步更新下游数据集的数据
                this.docCellOperator.updateSetByFileId(bucket.getCode(), dto.getFilter().getFileIds());
            }

        }
        mongoTemplate.remove(query, bucket.getCode());
    }

    @Override
    public void correct(String bucketCode, String cellId, Map<String, Object> updateCell) {
        if (CollectionUtil.isEmpty(updateCell)) {
            return;
        }
        GeneratedAssetBucket bucket = findBucket(bucketCode);
        if (AssetKindCode.IDX.equals(bucket.getKindCode())) {
            throw new LynxiaoException("索引库数据单元不允许修订!");
        }
        AssetKind assetKind = findAssetKind(bucket.getKindCode());
        MongoTemplate mongoTemplate = this.assetMongo.getMongoTemplate(assetKind.getStorageConfig());
        Document document = mongoTemplate.findById(Long.valueOf(cellId), Document.class, bucket.getCode());
        updateCell.forEach((key, value) -> {
            if (AssetCell.ID.equals(key)) {
                return;
            }
            if (value == null) {
                throw new LynxiaoException("修订字段值不能为空!");
            } else {
                document.put(key, value);
            }
        });
        // 更新修订时间戳
        document.putIfAbsent(AssetCellProps.PRESET_ATTRIBUTE, new Document());
        document.getEmbedded(List.of(AssetCellProps.PRESET_ATTRIBUTE), Document.class)
                .put(AssetCellProps.UTS, System.currentTimeMillis());

        Document encodeDoc;
        try {
            // 重新正文缓存
            DatashardEncoder.EncodeDocumentRequest request = new DatashardEncoder.EncodeDocumentRequest(String.format("%s:%s", bucketCode, cellId), document);
            List<Map<String, Object>> encodeDocs = this.datashardEncoder.encode(List.of(request));
            encodeDoc = new Document(encodeDocs.getFirst());
        } catch (Exception e) {
            log.error("DataShard Service Has Error: {}", e.getMessage());
            throw new LynxiaoException("编码失败，请检查数据格式是否正确!");
        }
        mongoTemplate.save(encodeDoc, bucket.getCode());
    }

    @Override
    public void export(CellExportDTO dto, HttpServletResponse response) {
        log.info("Exporting cells with query: {}", dto);
        GeneratedAssetBucket bucket = findBucket(dto.getFilter().getBucketCode());
        cellQueryFactory.buildCellQuery(bucket).export(bucket, dto, response);
    }

    @Override
    public void entry(CellEntryDTO dto) {
        log.info("entry cell: {}", dto);
        try {
            // 1、获取桶信息
            GeneratedAssetBucket bucket = findBucket(dto.getBucketCode());
            // 2、获取桶操作类
            AssetKind assetKind = findAssetKind(bucket.getKindCode());
            MongoTemplate mongoTemplate = this.assetMongo.getMongoTemplate(assetKind.getStorageConfig());
            // 3、给数据设置系统字段
            AssetCellProps props = AssetCellProps.createWithDefaults();
            // 源桶
            props.setSourceBucketCode(bucket.getCode());
            // 审核方式
            AssetAuditStatus auditStatus = AssetAuditStatus.of(bucket.getAuditType());
            props.setAuditStatus(auditStatus, new AssetAuditor());
            // 手动标志
            props.setManual(AssetCellProps.BoolValue.TRUE);
            // 遍历赋值
            List<AssetCell> assetCells = new ArrayList<>();
            for (String item : dto.getDataList()) {
                AssetCell cell = AssetCell.from(JSONObject.parseObject(item));
                cell.setId(snowflakeIdGenerator.generateId());
                cell.put(AssetCellProps.PRESET_ATTRIBUTE, props);
                assetCells.add(cell);
            }
            // 3、将数据缓存到datashard
            List<DatashardEncoder.EncodeDocumentRequest> requestList = assetCells.stream().map(assetCell -> {
                DatashardEncoder.EncodeDocumentRequest encodeDocumentRequest = new DatashardEncoder.EncodeDocumentRequest();
                encodeDocumentRequest.setDocument(assetCell);
                encodeDocumentRequest.setRefIdPrefix(String.format("%s:%s", bucket.getCode(), assetCell.getId()));
                return encodeDocumentRequest;
            }).toList();

            List<Map<String, Object>> encodeList = this.datashardEncoder.encode(requestList);
            if (!CollectionUtils.isEmpty(encodeList)) {
                List<Document> documents = encodeList.stream().map(Document::new).toList();
                mongoTemplate.insert(documents, bucket.getCode());
            }
        } catch (Exception e) {
            log.error("data entry has ex:", e);
            throw new LynxiaoException("数据录入失败!".concat(e.getMessage()));
        }
    }


    private GeneratedAssetBucket findBucket(String bucketCode) {
        return this.bucketRepository.findByCodeAndDeletedFalse(bucketCode)
                .orElseThrow(() -> new LynxiaoException("数据桶不存在!"));
    }


    private Page<AssetCell> decodePageDoc(Page<AssetCell> sourceDoc) {
        List<AssetCell> content = sourceDoc.getContent();
        List<Map<String, Object>> list = content.stream().map(AssetCell::toMap).toList();
        try {
            List<Map<String, Object>> decode = this.datashardDecoder.decode(list);
            List<AssetCell> documents = decode.stream().map(AssetCell::from).toList();
            return new PageImpl<>(documents, sourceDoc.getPageable(), sourceDoc.getTotalElements());
        } catch (Exception e) {
            log.error("正文获取失败！", e);
            return sourceDoc;
        }
    }

    private AssetKind findAssetKind(String kindCode) {
        return this.kindRepository.findByCodeAndDeletedFalse(kindCode)
                .map(AssetKind::of)
                .orElseThrow(() -> new LynxiaoException("资产类型不存在!"));
    }

    @Override
    public Page<AssetCell> mongoDataDetail(String bucketCode, String query, Pageable pageable){
        Map<String, Object> cellQuery = ExprUtil.toMongoQuery(JSONObject.parseObject(query));
        GeneratedAssetBucket bucket = findBucket(bucketCode);
        MongoCursor<Document> documents = this.docRepository.findDocs(bucketCode,cellQuery);
        List<AssetCell> assetCells = new ArrayList<>();
        while (documents.hasNext()) {
            Document doc = documents.next();
            assetCells.add(AssetCell.from(doc));
        }
        return new PageImpl<>(assetCells, pageable, assetCells.size());
    }

}

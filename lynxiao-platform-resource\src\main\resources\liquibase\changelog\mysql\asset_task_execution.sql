--liquibase formatted sql
--changeset skynet:create_table_asset_task_execution
CREATE TABLE `asset_task_execution`( 
    `id` int NOT NULL AUTO_INCREMENT,
    `task_id` bigint COMMENT '关联任务ID', 
    `flow_id` bigint COMMENT '流程快照', 
    `astrolink_id` varchar(100) COMMENT '星链流程ID', 
    `source_buckets` text COMMENT '源桶列表', 
    `status` int COMMENT '任务状态', 
    `message` varchar(100) COMMENT '错误信息', 
    `step` int COMMENT '子任务步骤', 
    `execution_id` bigint COMMENT '执行记录ID', 
    `type` int COMMENT '任务类型', 
    `debug` int COMMENT '是否为调试', 
    `start_time` datetime COMMENT '开始时间', 
    `end_time` datetime COMMENT '结束时间', 
    `total` bigint COMMENT '任务总量', 
    `done` bigint COMMENT '已处理数据量', 
    `fail` bigint COMMENT '失败数据量', 
    `total_should` int COMMENT '应该收到几次任务总量的消息，也就是流程中源桶的个数', 
    `total_real` int COMMENT '实际收到几次的任务总量的消息', 
    `auto` tinyint COMMENT '是否自动', 
    `execute_param` text COMMENT '执行参数，仅非流程任务会保存', 
    `created_by` varchar(100) COMMENT '创建人',
    `created_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `last_modified_by` varchar(100) COMMENT '更新人',
    `last_modified_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` varchar(100) COMMENT '租户',
    PRIMARY KEY (`id`),
    INDEX `idx_created_by` (`created_by`),
    INDEX `idx_created_date` (`created_date`),
    INDEX `idx_last_modified_by` (`last_modified_by`),
    INDEX `idx_last_modified_date` (`last_modified_date`),
    INDEX `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin;


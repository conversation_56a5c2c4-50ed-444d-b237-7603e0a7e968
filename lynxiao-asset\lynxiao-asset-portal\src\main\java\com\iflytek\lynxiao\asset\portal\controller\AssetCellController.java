package com.iflytek.lynxiao.asset.portal.controller;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.asset.portal.dto.cell.*;
import com.iflytek.lynxiao.asset.portal.service.AssetCellService;
import com.iflytek.lynxiao.asset.portal.service.cell.AssetCellAuditService;
import com.iflytek.lynxiao.asset.portal.service.cell.AssetDocCellOperator;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.data.dto.asset.CellQueryDTO;
import com.iflytek.lynxiao.data.dto.asset.QueryInvalidCellCountDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.gridfs.GridFsResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;
import skynet.boot.annotation.EnableSkynetSwagger2;
import skynet.boot.pandora.api.ApiResponse;

import java.io.InputStream;
import java.util.Map;

/**
 * 数据单元预览
 */
@Tag(name = "数据单元")
@RestController
@RequestMapping("/asset/api/v1/cell")
@EnableSkynetSwagger2
@Slf4j
public class AssetCellController {

    private final AssetCellService service;
    private final AssetCellAuditService auditService;
    private final AssetDocCellOperator cellOperator;

    public AssetCellController(AssetCellService service, AssetCellAuditService auditService,
                               AssetDocCellOperator cellOperator) {
        this.service = service;
        this.auditService = auditService;
        this.cellOperator = cellOperator;
    }

    @Operation(summary = "文件上传")
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ApiResponse upload(@RequestParam String bucketCode, @RequestPart(value = "files") MultipartFile[] files,
                              @RequestParam(value = "fileIds", required = false) String fileIdsJson) {

        Map<String, String> fileIds = null;
        if (fileIdsJson != null && !fileIdsJson.trim().isEmpty()) {
            try {
                fileIds = JSONObject.parseObject(fileIdsJson, Map.class);
            } catch (Exception e) {
                log.error("Invalid fileIds format, current fileIdsJson: {}", fileIdsJson);
                throw new LynxiaoException("Invalid fileIds format, current fileIdsJson: " + fileIdsJson);
            }
        }

        Map<String, String> result = this.cellOperator.upload(bucketCode, files, fileIds);
        return new ApiResponse(JSONObject.of("data", result));
    }

    @Operation(summary = "文件下载")
    @GetMapping(value = "/download/{bucketCode}/{id}")
    public ResponseEntity<StreamingResponseBody> download(@PathVariable String bucketCode, @PathVariable String id) {
        GridFsResource resource = this.cellOperator.download(bucketCode, id);
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + resource.getFilename())
                .contentType(MediaType.parseMediaType(resource.getContentType()))
                .body(outputStream -> {
                    try (InputStream inputStream = resource.getInputStream()) {
                        IOUtils.copy(inputStream, outputStream);
                    }
                });
    }

    @Operation(summary = "查询数据单元数量")
    @GetMapping("/count")
    public ApiResponse count(@Validated CellQueryDTO dto) {
        return new ApiResponse(JSONObject.of("data", this.service.count(dto)));
    }

    @Operation(summary = "查询无效数据单元数量")
    @GetMapping("/invalidCount")
    public ApiResponse invalidCount(@Validated QueryInvalidCellCountDTO dto) {
        return new ApiResponse(JSONObject.of("data", this.service.invalidCount(dto)));
    }

    @Operation(summary = "预览数据单元")
    @GetMapping("/list")
    public ApiResponse list(@Validated CellQueryDTO dto, Pageable pageable) {
        return new ApiResponse(JSONObject.of("data", this.service.list(dto, pageable)));
    }

    @Operation(summary = "查询数据单元详情")
    @GetMapping("/detail")
    public ApiResponse detail(@Validated CellDetailQueryDTO dto) {
        return new ApiResponse(JSONObject.from(this.service.detail(dto)));
    }

    @Operation(summary = "删除")
    @DeleteMapping("/delete")
    public ApiResponse delete(@Validated @RequestBody CellDeleteDTO dto) {
        this.service.delete(dto);
        return new ApiResponse();
    }

    @Operation(summary = "修订")
    @PostMapping("/correct/{bucketCode}/{id}")
    public ApiResponse correct(@PathVariable String bucketCode, @PathVariable String id,
                               @RequestBody Map<String, Object> updateCell) {
        this.service.correct(bucketCode, id, updateCell);
        return new ApiResponse();
    }

    @Operation(summary = "审核")
    @PutMapping("/audit")
    public ApiResponse audit(@Validated @RequestBody CellAuditDTO dto) {
        this.auditService.audit(dto);
        return new ApiResponse();
    }

    @Operation(summary = "数据录入")
    @PostMapping("/entry")
    public ApiResponse dataEntry(@Validated @RequestBody CellEntryDTO dto) {
        this.service.entry(dto);
        return new ApiResponse();
    }

    @Operation(summary = "导出数据")
    @GetMapping("/export")
    public void export(@Validated CellExportDTO dto, HttpServletResponse response) {
        this.service.export(dto, response);
    }

    @Operation(summary = "mongo查询")
    @GetMapping("/mongo/detail")
    public ApiResponse mongoDetail(@RequestParam String bucketCode, @RequestParam String query, Pageable pageable) {
        return new ApiResponse(JSONObject.from(this.service.mongoDataDetail(bucketCode, query, pageable)));
    }
}

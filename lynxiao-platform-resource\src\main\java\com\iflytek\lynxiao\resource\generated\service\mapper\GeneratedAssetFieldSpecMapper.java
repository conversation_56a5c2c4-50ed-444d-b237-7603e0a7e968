package com.iflytek.lynxiao.resource.generated.service.mapper;

import com.iflytek.lynxiao.resource.generated.domain.GeneratedAssetFieldSpec;
import com.iflytek.lynxiao.resource.service.dto.AssetFieldSpecDTO;
import skynet.boot.common.mapper.EntityMapper;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;

@Mapper(
    componentModel = "spring",
    uses = {},
    builder = @Builder(disableBuilder = true))
public interface GeneratedAssetFieldSpecMapper extends EntityMapper<AssetFieldSpecDTO, GeneratedAssetFieldSpec> {

}
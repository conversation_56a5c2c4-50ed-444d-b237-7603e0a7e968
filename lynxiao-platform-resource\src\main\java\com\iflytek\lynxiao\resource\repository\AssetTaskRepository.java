package com.iflytek.lynxiao.resource.repository;

import com.iflytek.lynxiao.resource.generated.domain.GeneratedAssetTask;
import com.iflytek.lynxiao.resource.generated.repository.GeneratedAssetTaskRepository;

import java.util.List;

import org.springframework.stereotype.Repository;

@Repository
public interface AssetTaskRepository extends GeneratedAssetTaskRepository {

    /**
     * 根据桶编码查询任务
     * @param bucketCode 桶编码
     * @return 任务
     */
    List<GeneratedAssetTask> findByBucketCodeAndTypeIn(String bucketCode, List<Integer> type);

    /**
     * 批量查询桶任务
     */
    List<GeneratedAssetTask> findAllByIdIn(List<Long> ids);


    int deleteByIdIn(List<Long> ids);
}
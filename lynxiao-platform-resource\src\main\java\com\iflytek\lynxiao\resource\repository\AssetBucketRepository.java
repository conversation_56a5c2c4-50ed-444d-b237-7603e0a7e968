package com.iflytek.lynxiao.resource.repository;

import com.iflytek.lynxiao.resource.generated.domain.GeneratedAssetBucket;
import com.iflytek.lynxiao.resource.generated.repository.GeneratedAssetBucketRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AssetBucketRepository extends GeneratedAssetBucketRepository {

    long countByNameAndKindCodeAndDeletedFalse(String name, String kindCode);

    long countByNameAndCatalogCodeAndDeletedFalse(String name, String catalogCode);

    @Query("SELECT m FROM GeneratedAssetBucket m WHERE m.deleted is false " +
            "AND m.kindCode = :kindCode " +
            "AND m.catalogCode IN :catalogCodeList " +
            "AND (LOWER(m.name) LIKE LOWER(CONCAT('%', :search, '%')) OR LOWER(m.code) LIKE LOWER(CONCAT('%', :search, '%')) OR LOWER(m.enName) LIKE LOWER(CONCAT('%', :search, '%')))")
    Page<GeneratedAssetBucket> search(@Param("search") String search, @Param("kindCode") String kindCode, @Param("catalogCodeList") List<String> catalogCodeList, Pageable pageable);

    @Query("SELECT m FROM GeneratedAssetBucket m WHERE m.deleted is false AND m.kindCode = :kindCode  AND m.catalogCode IN :catalogCodeList ")
    Page<GeneratedAssetBucket> search(@Param("kindCode") String kindCode, @Param("catalogCodeList") List<String> catalogCodeList, Pageable pageable);

    @Query("SELECT m FROM GeneratedAssetBucket m WHERE m.deleted is false " +
            "AND (LOWER(m.name) LIKE LOWER(CONCAT('%', :search, '%')) OR LOWER(m.code) LIKE LOWER(CONCAT('%', :search, '%')) OR LOWER(m.enName) LIKE LOWER(CONCAT('%', :search, '%'))) " +
            "AND (:kindCodes IS NULL OR m.kindCode IN :kindCodes) " +
            "AND (:status IS NULL OR m.status = :status) " +
            "AND (:enabled IS NULL OR m.enabled = :enabled) " +
            "AND (:catalogCode IS NULL OR m.catalogCode = :catalogCode)" +
            "AND (:bucketCodes IS NULL OR m.code IN :bucketCodes)")
    List<GeneratedAssetBucket> search(@Param("search") String search,
                                      @Param("kindCodes") List<String> kindCodes,
                                      @Param("status") Integer status,
                                      @Param("enabled") Boolean enabled,
                                      @Param("catalogCode") String catalogCode,
                                      @Param("bucketCodes") List<String> bucketCodes,
                                      Sort sort);

    Optional<GeneratedAssetBucket> findByCodeAndDeletedFalse(String code);

    List<GeneratedAssetBucket> findByCodeInAndDeletedFalse(List<String> code);

    Optional<GeneratedAssetBucket> findByIdAndDeletedFalse(Long id);

    List<GeneratedAssetBucket> findBySpaceCodeAndDeletedFalse(String spaceCode);

    @Query("SELECT m FROM GeneratedAssetBucket m WHERE m.deleted is false " +
            "AND (:kindCodes IS NULL OR m.kindCode IN :kindCodes) " +
            "AND (:status IS NULL OR m.status = :status) " +
            "AND (:enabled IS NULL OR m.enabled = :enabled) " +
            "AND (:catalogCode IS NULL OR m.catalogCode = :catalogCode)" +
            "AND (:bucketCodes IS NULL OR m.code IN :bucketCodes)")
    List<GeneratedAssetBucket> findAll(@Param("kindCodes") List<String> kindCodes,
                                       @Param("status") Integer status,
                                       @Param("enabled") Boolean enabled,
                                       @Param("catalogCode") String catalogCode,
                                       @Param("bucketCodes") List<String> bucketCodes,
                                       Sort sort);

    @Query("SELECT m FROM GeneratedAssetBucket m WHERE m.deleted is false AND m.catalogCode IN :catalogCodeList")
    List<GeneratedAssetBucket> findAllByCatalog(@Param("catalogCodeList") List<String> catalogCodeList);

    @Query("SELECT MAX(m.orderNum) FROM GeneratedAssetBucket m WHERE m.deleted is false")
    Integer findMaxOrderNum();

    // 根据 catalogCode数组，聚合查询所有桶的资产类型kind_code
    @Query("SELECT DISTINCT m.kindCode FROM GeneratedAssetBucket m WHERE m.deleted is false AND m.catalogCode IN :catalogCodes")
    List<String> findBucketKinds(@Param("catalogCodes") List<String> catalogCodes);

    @Query("SELECT m FROM GeneratedAssetBucket m WHERE m.deleted is false AND m.id in :ids ORDER BY m.createdDate DESC")
    List<GeneratedAssetBucket> findByIds(@Param("ids") List<Long> ids);

    @Modifying
    @Query("UPDATE GeneratedAssetBucket m SET m.deleted = TRUE WHERE m.id = :id")
    void deleteById(@Param("id") Long id);

    @Modifying
    @Query("UPDATE GeneratedAssetBucket m SET m.deleted = TRUE WHERE m.code = :code")
    void deleteByCode(@Param("code") String code);

    @Modifying
    @Query("UPDATE GeneratedAssetBucket m SET m.enabled= :enabled  WHERE  m.code = :code AND m.deleted = false")
    void enabledByCode(@Param("code") String code, @Param("enabled") boolean enabled);

    @Query("SELECT m FROM GeneratedAssetBucket m WHERE m.deleted is false AND m.moduleCode = :moduleCode AND m.spaceCode = :spaceCode")
    List<GeneratedAssetBucket> findByModuleAndSpaceCode(@Param("spaceCode") String spaceCode, @Param("moduleCode") String moduleCode);

}
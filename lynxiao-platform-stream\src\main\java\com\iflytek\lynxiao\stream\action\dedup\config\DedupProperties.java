package com.iflytek.lynxiao.stream.action.dedup.config;

import com.iflytek.lynxiao.data.constant.ScriptType;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 配置项
 *
 * <AUTHOR>  2024/9/25 15:01
 */
@Getter
@Setter
public class DedupProperties extends Jsonable {

    private boolean enabled = false;


    private DedupTaskConfig taskConfig = new DedupTaskConfig();

    private DedupDocConfig docConfig = new DedupDocConfig();

    /**
     * 去重目标库 MongoDB URI Map ，
     * <p>
     * lynxiao.stream.dedup.target-mongo-uri.lynxiao_label=${lynxiao.label-mongo.uri}
     */
    private Map<String, String> targetMongoUri = new HashMap<>();


    /**
     * 生成去重计算任务消息 相关配置
     */
    @Getter
    @Setter
    public static class DedupTaskConfig {

        /**
         * 每个文档集合，切分组数
         */
        private int partition = 20;

        /**
         * 分页查询大小
         */
        private int pageSize = 1000;

        /**
         * 查询文档超时时间
         */
        private Duration queryDocTimeout = Duration.ofHours(2);

        /**
         * 异步管道大小
         */
        private int pipelineQueueSize = 10000;

        /**
         * 异步管道写入流量限制，超过暂停写入
         */
        private int pipelineWriteLimit = 1000;

        /**
         * 异步管道写入触发限流后，等待时间
         */
        private Duration pipelineWriteWait = Duration.ofMillis(1000);

        /**
         * 布隆过滤器初试大小 1.2亿
         */
        private int expectedInsertions = 12000 * 10000;

        /**
         * 布隆过滤器假阳性率
         */
        private double falsePositiveProbability = 0.001;

        /**
         * gid缓存时间
         */
        private Duration gidTtl = Duration.ofHours(2);

        /**
         * gid缓存扫描大小
         */
        private int gidScanPageSize = 500;

        /**
         * 进度统计间隔周期，默认1分钟统计一次
         */
        private Duration statisticsPeriod = Duration.ofMinutes(1);
    }


    /**
     * 生成去重计算任务消息 相关配置
     */
    @Getter
    @Setter
    public static class DedupDocConfig {

        /**
         * 去重脚本类型
         */
        private ScriptType scriptType = ScriptType.GROOVY;

        private Integer dedupPoolSize = 20;

        private Integer dedupQueueSize = 200;

        /**
         * 阻塞等待去重计算结束
         */
        private Duration timeout = Duration.ofMinutes(5);
    }
}

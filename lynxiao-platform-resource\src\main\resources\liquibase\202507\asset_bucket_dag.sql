
-- 添加edge字段
ALTER TABLE `asset_bucket_dag`
ADD COLUMN `edge` int DEFAULT 1 COMMENT '边类型：1-不同桶关系，2-同桶同步关系';

-- 根据规则更新edge值
UPDATE `asset_bucket_dag`
SET `edge` = CASE
    WHEN `source_bucket_code` = `target_bucket_code` THEN 2
    ELSE 1
END;

-- 插入数据连接关系
INSERT  INTO lynxiao_portal.asset_bucket_dag (source_bucket_code,target_bucket_code,flow_id,space_code,deleted,created_by,created_date,last_modified_by,last_modified_date,tenant_id,edge)
SELECT
	asset_source_id as source_bucket_code,
	bucket_code as target_bucket_code ,
	id as flow_id ,
	SUBSTRING_INDEX(bucket_code, '_', 1) as space_code ,
	0,
	created_by,
	created_date,
	last_modified_by,
	last_modified_date ,
	tenant_id ,
	0
FROM lynxiao_portal.asset_rule;
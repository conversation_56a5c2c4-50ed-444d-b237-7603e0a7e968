package com.iflytek.lynxiao.resource.repository;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.resource.domain.AssetTaskBatch;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.model.Filters;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.CollectionOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@ConditionalOnProperty(value = "lynxiao.platform-mongo.enabled")
public class AssetTaskBatchRepository {

    // 集合最大数据量：1亿
    public static final int CAPPED_MAX = 100000000;

    // 集合最大数据大小：200GB
    public static final long CAPPED_SIZE = 200 * (long) Math.pow(2, 30);

    private final MongoTemplate template;

    public AssetTaskBatchRepository(@Qualifier("platformMongoTemplate") MongoTemplate template) {
        this.template = template;
    }

    @PostConstruct
    private void init() {
        if (!this.template.collectionExists(AssetTaskBatch.class)) {
            CollectionOptions options = CollectionOptions.empty().capped().size(CAPPED_SIZE).maxDocuments(CAPPED_MAX);
            this.template.createCollection(AssetTaskBatch.class, options);
        }
    }

    /**
     * 保存批次记录
     */
    public void save(AssetTaskBatch entity) {
        this.template.save(entity);
    }

    /**
     * 判断批次记录是否存在
     */
    public boolean existsByBatchId(String batchId) {
        return this.template.exists(
            Query.query(Criteria.where("batchId").is(batchId)), 
            AssetTaskBatch.class
        );
    }

    /**
     * 判断批次成功记录是否存在
     */
    public boolean existsSuccessByBatchId(String batchId) {
        return this.template.exists(
            Query.query(Criteria.where("batchId").is(batchId).and("code").is(0)), 
            AssetTaskBatch.class
        );
    }

    /**
     * 删除批次记录
     */
    public long deleteByBatchId(String batchId) {
        return this.template.remove(
            Query.query(Criteria.where("batchId").is(batchId)), 
            AssetTaskBatch.class
        ).getDeletedCount();
    }

    /**
     * 删除某次执行记录的所有批次记录
     */
    public long deleteByExecutionId(Long executionId, int code) {
        return this.template.remove(
            Query.query(Criteria.where("executionId").is(executionId).and("code").is(code)), 
            AssetTaskBatch.class
        ).getDeletedCount();
    }

    /**
     * 更新某次执行记录的所有批次记录，将 code 置为 0
     */
    public long resetByExecutionId(Long executionId, JSONObject status) {
        Query query = new Query(Criteria.where("executionId").is(executionId));
        Update update = new Update().set("code", 0).set("status", status)
                .set("message", "")
                .set("lastModifiedDate", Instant.now());
        return this.template.updateMulti(query, update, AssetTaskBatch.class).getModifiedCount();
    }

    /**
     * 查找批次记录
     */
    public AssetTaskBatch findByBatchId(String batchId) {
        Query query = new Query(Criteria.where("batchId").is(batchId));
        return this.template.findOne(query, AssetTaskBatch.class);
    }

    /**
     * 更新 code 和 message 字段
     * 只更新成功记录，当批次已标记为失败时，不再更新
     */
    public long updateCodeMessage(String batchId, int code, String message, JSONObject node) {
        Query query = new Query(Criteria.where("batchId").is(batchId).and("code").is(0));
        Update update = new Update().set("code", code)
                .set("message", message)
                .set("node", node)
                .set("lastModifiedDate", Instant.now());
        return this.template.updateFirst(query, update, AssetTaskBatch.class).getModifiedCount();
    }

    /**
     * 更新节点运行状态，累加当前节点 done
     */
    public long updateStatus(String batchId, String currentNode, long done, String version) {
        Query query = new Query(Criteria.where("batchId").is(batchId).and("code").is(0));
        Update update = new Update().set("lastModifiedDate", Instant.now()).set("version", version);
        update = update.inc("status." + currentNode + ".done", done);
        return this.template.updateFirst(query, update, AssetTaskBatch.class).getModifiedCount();
    }

    /**
     * 更新节点运行状态，累加当前节点 done 和下游节点 total
     */
    public long updateStatus(String batchId, String currentNode, long done, List<String> nextNodes, long total) {
        Query query = new Query(Criteria.where("batchId").is(batchId).and("code").is(0));
        Update update = new Update().set("lastModifiedDate", Instant.now());
        update = update.inc("status." + currentNode + ".done", done);
        for (String nextNode : nextNodes) {
            update = update.inc("status." + nextNode + ".total", total);
        }
        return this.template.updateFirst(query, update, AssetTaskBatch.class).getModifiedCount();
    }

    /**
     * 根据任务id查询不等于某个错误码的所有记录
     */
    public MongoCursor<Document> findByExecutionId(Long taskId, int limit) {
        MongoCollection<Document> collection = this.template.getCollection(AssetTaskBatch.COL);
        List<Bson> query = new ArrayList<>();
        query.add(Filters.eq("executionId", taskId));
        return collection.find(Filters.and(query), Document.class).batchSize(limit).noCursorTimeout(true).cursor();
    }

    public void deleteByIdIn(List<String> ids) {
        this.template.remove(Query.query(Criteria.where("_id").in(ids)), AssetTaskBatch.class);
    }

    /**
     * 条件查询 聚合docId
     *
     * @param criteria 条件
     * @param pageable 分页 (为null时查询所有数据)
     * @return 返回docId列表
     */
    public List<ItemResult> findFailedDoc(Criteria criteria, Pageable pageable) {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.unwind("docIds"),
                Aggregation.project()
                        .and("docIds").as("docId")
                        .and("batchId").as("batchId")
                        .and("code").as("code")
                        .and("message").as("message"),
                Aggregation.skip((long) pageable.getPageNumber() * pageable.getPageSize()),
                Aggregation.limit(pageable.getPageSize())        );
        AggregationResults<ItemResult> results = this.template.aggregate(aggregation, AssetTaskBatch.COL, ItemResult.class);
        // 获取结果
        List<ItemResult> result = results.getMappedResults();
        return !CollectionUtils.isEmpty(result) ? result : Collections.emptyList() ;
    }

    /**
     * 条件查询doc总数
     *
     * @param criteria 条件
     * @return 总数
     */
    public long countFailedDoc(Criteria criteria) {

        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria), // 过滤 taskId
                Aggregation.unwind("docIds"), // 展开 ids 数组
                Aggregation.group("taskId").count().as("totalCount")// 分组并收集 ids
        );

        AggregationResults<Map> results = this.template.aggregate(aggregation, AssetTaskBatch.COL, Map.class);
        // 获取结果
        Map<String, Object> result = results.getUniqueMappedResult();
        return result != null ? result.get("totalCount") != null ? Long.parseLong(String.valueOf(result.get("totalCount"))) : 0L : 0L;
    }

    @Setter
    @Getter
    public static class ItemResult {
        // 文档id
        private Long docId;

        // 批次id
        private String batchId;

        // 错误信息
        private String message;

        // 错误码
        private Integer code;
    }
}
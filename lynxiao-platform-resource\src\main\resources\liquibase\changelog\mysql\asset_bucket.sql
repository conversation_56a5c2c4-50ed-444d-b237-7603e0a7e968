--liquibase formatted sql
--changeset skynet:create_table_asset_bucket
CREATE TABLE `asset_bucket`( 
    `id` int NOT NULL AUTO_INCREMENT,
    `name` varchar(100) COMMENT '桶名称', 
    `code` varchar(100) COMMENT '桶编码，命名空间编码+资产类型编码+12位随机编码', 
    `description` varchar(400) COMMENT '桶描述', 
    `en_name` varchar(100) COMMENT '桶英文名称', 
    `kind_code` varchar(100) COMMENT '资产类型, 如 SITE，DOC', 
    `ttl` bigint COMMENT '存储周期，单位小时, 如：最近7天，0 为无限期；', 
    `audit_type` int COMMENT '审核方式: 1：先审 、2：后审', 
    `support_correct` tinyint COMMENT '是否支持修订（桶内数据是否能编辑修改更新）', 
    `tags` varchar(100) COMMENT '标签', 
    `cell_fields` varchar(100) COMMENT '数据列字段（数据规格）', 
    `index_fields` varchar(100) COMMENT '索引属性', 
    `enabled` tinyint COMMENT '是否启用', 
    `status` int COMMENT '状态 1: 草稿；2：已发布', 
    `ext_config` text COMMENT '扩展属性，不同的数据类型属性不一样，由各自解释', 
    `space_code` varchar(100) COMMENT '归属空间编码', 
    `module_code` varchar(100) COMMENT '归属模块编码', 
    `catalog_code` varchar(100) COMMENT '归属目录编码', 
    `order_num` int COMMENT '顺序号', 
    `deleted` tinyint COMMENT '删除标志', 
    `field_spec` text COMMENT '规格模板', 
    `created_by` varchar(100) COMMENT '创建人',
    `created_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `last_modified_by` varchar(100) COMMENT '更新人',
    `last_modified_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` varchar(100) COMMENT '租户',
    PRIMARY KEY (`id`),
    INDEX `idx_created_by` (`created_by`),
    INDEX `idx_created_date` (`created_date`),
    INDEX `idx_last_modified_by` (`last_modified_by`),
    INDEX `idx_last_modified_date` (`last_modified_date`),
    INDEX `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin;

